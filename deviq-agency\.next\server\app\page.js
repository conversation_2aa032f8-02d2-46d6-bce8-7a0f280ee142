/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3451d6d025db\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWF5UmFrZ2FtYVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxEZXZJUVxcZGV2aXEtYWdlbmN5XFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzQ1MWQ2ZDAyNWRiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_300_400_500_600_700_800_variableName_poppins___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"variable\":\"--font-poppins\",\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"]}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-poppins\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"]}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_300_400_500_600_700_800_variableName_poppins___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_300_400_500_600_700_800_variableName_poppins___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"AMPD Dev-IQ - Smart Software. Limitless Potential.\",\n    description: \"We amplify your business with intelligent software solutions that unlock unlimited growth potential. Custom web development, mobile apps, enterprise software, AI/ML solutions, and more.\",\n    keywords: \"smart software, intelligent solutions, web development, mobile apps, AI solutions, enterprise software, custom development, limitless potential, AMPD DevIQ\",\n    authors: [\n        {\n            name: \"AMPD Dev-IQ\"\n        }\n    ],\n    openGraph: {\n        title: \"AMPD Dev-IQ - Smart Software. Limitless Potential.\",\n        description: \"We amplify your business with intelligent software solutions that unlock unlimited growth potential.\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Poppins_arguments_variable_font_poppins_subsets_latin_weight_300_400_500_600_700_800_variableName_poppins___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased bg-white text-gray-900 font-inter`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\layout.tsx\",\n            lineNumber: 35,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\layout.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ModernSinglePage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ModernSinglePage */ \"(rsc)/./components/ModernSinglePage.tsx\");\n/* harmony import */ var _components_LiveChat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/LiveChat */ \"(rsc)/./components/LiveChat.tsx\");\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSinglePage__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\page.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LiveChat__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThEO0FBQ2hCO0FBRS9CLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osb0VBQWdCQTs7Ozs7MEJBQ2pCLDhEQUFDQyw0REFBUUE7Ozs7Ozs7Ozs7O0FBR2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTWF5UmFrZ2FtYVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxEZXZJUVxcZGV2aXEtYWdlbmN5XFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBNb2Rlcm5TaW5nbGVQYWdlIGZyb20gJy4uL2NvbXBvbmVudHMvTW9kZXJuU2luZ2xlUGFnZSc7XG5pbXBvcnQgTGl2ZUNoYXQgZnJvbSAnLi4vY29tcG9uZW50cy9MaXZlQ2hhdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cbiAgICAgIDxNb2Rlcm5TaW5nbGVQYWdlIC8+XG4gICAgICA8TGl2ZUNoYXQgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuXG4iXSwibmFtZXMiOlsiTW9kZXJuU2luZ2xlUGFnZSIsIkxpdmVDaGF0IiwiSG9tZSIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/LiveChat.tsx":
/*!*********************************!*\
  !*** ./components/LiveChat.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\DevIQ\\deviq-agency\\components\\LiveChat.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\DevIQ\\deviq-agency\\components\\ModernSinglePage.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/LiveChat.tsx */ \"(rsc)/./components/LiveChat.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ModernSinglePage.tsx */ \"(rsc)/./components/ModernSinglePage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01heVJha2dhbWElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDRGV2SVElNUMlNUNkZXZpcS1hZ2VuY3klNUMlNUNjb21wb25lbnRzJTVDJTVDTGl2ZUNoYXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNNYXlSYWtnYW1hJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0RldklRJTVDJTVDZGV2aXEtYWdlbmN5JTVDJTVDY29tcG9uZW50cyU1QyU1Q01vZGVyblNpbmdsZVBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1LO0FBQ25LO0FBQ0EsOEtBQTJLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcTWF5UmFrZ2FtYVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxEZXZJUVxcXFxkZXZpcS1hZ2VuY3lcXFxcY29tcG9uZW50c1xcXFxMaXZlQ2hhdC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxNYXlSYWtnYW1hXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXERldklRXFxcXGRldmlxLWFnZW5jeVxcXFxjb21wb25lbnRzXFxcXE1vZGVyblNpbmdsZVBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNYXlSYWtnYW1hXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXERldklRXFxkZXZpcS1hZ2VuY3lcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./components/AdvancedAnimations.tsx":
/*!*******************************************!*\
  !*** ./components/AdvancedAnimations.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedText: () => (/* binding */ AnimatedText),\n/* harmony export */   FloatingParticles: () => (/* binding */ FloatingParticles),\n/* harmony export */   GlitchText: () => (/* binding */ GlitchText),\n/* harmony export */   InteractiveBlob: () => (/* binding */ InteractiveBlob),\n/* harmony export */   LiquidButton: () => (/* binding */ LiquidButton),\n/* harmony export */   MorphingBackground: () => (/* binding */ MorphingBackground),\n/* harmony export */   ParallaxSection: () => (/* binding */ ParallaxSection),\n/* harmony export */   useScrollAnimation: () => (/* binding */ useScrollAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FloatingParticles,MorphingBackground,ParallaxSection,InteractiveBlob,useScrollAnimation,AnimatedText,LiquidButton,GlitchText auto */ \n\n\n// Floating Particles Component\nconst FloatingParticles = ()=>{\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingParticles.useEffect\": ()=>{\n            const newParticles = Array.from({\n                length: 50\n            }, {\n                \"FloatingParticles.useEffect.newParticles\": (_, i)=>({\n                        id: i,\n                        x: Math.random() * 100,\n                        y: Math.random() * 100,\n                        size: Math.random() * 4 + 1,\n                        color: [\n                            '#22d3ee',\n                            '#a855f7',\n                            '#f97316',\n                            '#10b981'\n                        ][Math.floor(Math.random() * 4)]\n                    })\n            }[\"FloatingParticles.useEffect.newParticles\"]);\n            setParticles(newParticles);\n        }\n    }[\"FloatingParticles.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n        children: particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute rounded-full opacity-60\",\n                style: {\n                    left: `${particle.x}%`,\n                    top: `${particle.y}%`,\n                    width: `${particle.size}px`,\n                    height: `${particle.size}px`,\n                    backgroundColor: particle.color,\n                    boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`\n                },\n                animate: {\n                    y: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        -15,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.2,\n                        0.8,\n                        1\n                    ],\n                    opacity: [\n                        0.6,\n                        1,\n                        0.3,\n                        0.6\n                    ]\n                },\n                transition: {\n                    duration: Math.random() * 10 + 10,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: Math.random() * 5\n                }\n            }, particle.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n};\n// Morphing Background Component\nconst MorphingBackground = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 -z-10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            className: \"absolute inset-0\",\n            animate: {\n                background: [\n                    'radial-gradient(circle at 20% 50%, rgba(34, 211, 238, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.4) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(249, 115, 22, 0.2) 0%, transparent 50%)',\n                    'radial-gradient(circle at 80% 50%, rgba(34, 211, 238, 0.4) 0%, transparent 50%), radial-gradient(circle at 20% 80%, rgba(168, 85, 247, 0.3) 0%, transparent 50%), radial-gradient(circle at 60% 20%, rgba(249, 115, 22, 0.3) 0%, transparent 50%)',\n                    'radial-gradient(circle at 50% 20%, rgba(34, 211, 238, 0.2) 0%, transparent 50%), radial-gradient(circle at 20% 50%, rgba(168, 85, 247, 0.4) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(249, 115, 22, 0.4) 0%, transparent 50%)',\n                    'radial-gradient(circle at 20% 50%, rgba(34, 211, 238, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.4) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(249, 115, 22, 0.2) 0%, transparent 50%)'\n                ]\n            },\n            transition: {\n                duration: 20,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n// Parallax Section Component\nconst ParallaxSection = ({ children, speed = 0.5 })=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)({\n        target: ref,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        speed * 100\n    ]);\n    const springY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useSpring)(y, {\n        stiffness: 100,\n        damping: 30\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        ref: ref,\n        style: {\n            y: springY\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n// Interactive Blob Component\nconst InteractiveBlob = ({ className = \"\" })=>{\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InteractiveBlob.useEffect\": ()=>{\n            const handleMouseMove = {\n                \"InteractiveBlob.useEffect.handleMouseMove\": (e)=>{\n                    if (ref.current) {\n                        const rect = ref.current.getBoundingClientRect();\n                        setMousePosition({\n                            x: e.clientX - rect.left,\n                            y: e.clientY - rect.top\n                        });\n                    }\n                }\n            }[\"InteractiveBlob.useEffect.handleMouseMove\"];\n            const element = ref.current;\n            if (element) {\n                element.addEventListener('mousemove', handleMouseMove);\n                return ({\n                    \"InteractiveBlob.useEffect\": ()=>element.removeEventListener('mousemove', handleMouseMove)\n                })[\"InteractiveBlob.useEffect\"];\n            }\n        }\n    }[\"InteractiveBlob.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: `relative overflow-hidden ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n            className: \"absolute w-96 h-96 rounded-full opacity-30 blur-3xl\",\n            style: {\n                background: 'linear-gradient(45deg, #22d3ee, #a855f7, #f97316)'\n            },\n            animate: {\n                x: mousePosition.x - 192,\n                y: mousePosition.y - 192,\n                scale: [\n                    1,\n                    1.2,\n                    1\n                ]\n            },\n            transition: {\n                type: \"spring\",\n                stiffness: 150,\n                damping: 15,\n                scale: {\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, undefined);\n};\n// Scroll-triggered Animation Hook\nconst useScrollAnimation = ()=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    return {\n        ref,\n        isInView\n    };\n};\n// Advanced Text Animation Component\nconst AnimatedText = ({ text, className = \"\", delay = 0 })=>{\n    const words = text.split(' ');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: words.map((word, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                initial: {\n                    opacity: 0,\n                    y: 50,\n                    rotateX: -90\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0,\n                    rotateX: 0\n                },\n                transition: {\n                    duration: 0.8,\n                    delay: delay + i * 0.1,\n                    ease: [\n                        0.25,\n                        0.46,\n                        0.45,\n                        0.94\n                    ]\n                },\n                className: \"inline-block mr-2\",\n                children: word\n            }, i, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, undefined);\n};\n// Liquid Button Component\nconst LiquidButton = ({ children, onClick, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n        className: `relative overflow-hidden px-8 py-4 rounded-2xl font-bold text-white ${className}`,\n        onClick: onClick,\n        whileHover: {\n            scale: 1.05\n        },\n        whileTap: {\n            scale: 0.95\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 bg-gradient-to-r from-cyan-500 via-purple-600 to-orange-500\",\n                animate: {\n                    backgroundPosition: [\n                        '0% 50%',\n                        '100% 50%',\n                        '0% 50%'\n                    ]\n                },\n                transition: {\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                },\n                style: {\n                    backgroundSize: '200% 200%'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 opacity-0\",\n                whileHover: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.3\n                },\n                style: {\n                    background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)'\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n// Glitch Effect Component\nconst GlitchText = ({ text, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 text-cyan-400\",\n                animate: {\n                    x: [\n                        0,\n                        -2,\n                        2,\n                        0\n                    ],\n                    opacity: [\n                        0,\n                        1,\n                        0,\n                        1,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 0.2,\n                    repeat: Infinity,\n                    repeatDelay: 3\n                },\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute inset-0 text-red-400\",\n                animate: {\n                    x: [\n                        0,\n                        2,\n                        -2,\n                        0\n                    ],\n                    opacity: [\n                        0,\n                        1,\n                        0,\n                        1,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 0.2,\n                    repeat: Infinity,\n                    repeatDelay: 3,\n                    delay: 0.1\n                },\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: text\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\AdvancedAnimations.tsx\",\n        lineNumber: 239,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/AdvancedAnimations.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LiveChat.tsx":
/*!*********************************!*\
  !*** ./components/LiveChat.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaperAirplaneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaperAirplaneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ComputerDesktopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaperAirplaneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaperAirplaneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,ComputerDesktopIcon,PaperAirplaneIcon,UserIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LiveChat = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            text: \"Hi! I'm AMPD Dev-IQ's AI assistant. How can I help you today?\",\n            sender: 'bot',\n            timestamp: new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        }\n    ]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const quickReplies = [\n        \"Tell me about your services\",\n        \"I need a quote\",\n        \"Schedule a consultation\",\n        \"View portfolio\"\n    ];\n    const botResponses = {\n        \"tell me about your services\": \"We offer comprehensive software development services including web development, mobile apps, enterprise software, AI/ML solutions, cloud architecture, and UI/UX design. Which service interests you most?\",\n        \"i need a quote\": \"I'd be happy to help you get a quote! Let me connect you with our team on WhatsApp for a detailed discussion about your project requirements.\",\n        \"schedule a consultation\": \"Great! I can help you schedule a free 30-minute consultation with our team. Let me connect you directly on WhatsApp: +27 79 448 4159\",\n        \"view portfolio\": \"You can view our portfolio in the Portfolio section above, or I can tell you about specific projects. We've worked on e-commerce platforms, healthcare systems, fintech apps, and more!\",\n        \"default\": \"Thanks for your message! For detailed assistance, let me connect you with our team on WhatsApp. We typically respond within 2 hours during business hours.\"\n    };\n    const connectToWhatsApp = (message)=>{\n        const whatsappUrl = `https://wa.me/***********?text=${encodeURIComponent(message)}`;\n        window.open(whatsappUrl, '_blank');\n    };\n    const handleSendMessage = ()=>{\n        if (!inputMessage.trim()) return;\n        const newMessage = {\n            id: messages.length + 1,\n            text: inputMessage,\n            sender: 'user',\n            timestamp: new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        // Simulate bot response\n        setTimeout(()=>{\n            const botResponse = botResponses[inputMessage.toLowerCase()] || botResponses.default;\n            const botMessage = {\n                id: messages.length + 2,\n                text: botResponse,\n                sender: 'bot',\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                })\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    botMessage\n                ]);\n            // Auto-connect to WhatsApp for certain queries\n            if (inputMessage.toLowerCase().includes('quote') || inputMessage.toLowerCase().includes('consultation')) {\n                setTimeout(()=>{\n                    connectToWhatsApp(`Hi! I was chatting with your AI assistant and would like to discuss: ${inputMessage}`);\n                }, 2000);\n            }\n        }, 1000);\n        setInputMessage('');\n    };\n    const handleQuickReply = (reply)=>{\n        setInputMessage(reply);\n        handleSendMessage();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                initial: {\n                    scale: 0\n                },\n                animate: {\n                    scale: 1\n                },\n                transition: {\n                    delay: 2,\n                    type: \"spring\",\n                    stiffness: 260,\n                    damping: 20\n                },\n                onClick: ()=>setIsOpen(true),\n                className: `fixed bottom-6 right-6 z-50 w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group ${isOpen ? 'hidden' : 'block'}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-8 h-8 text-white group-hover:scale-110 transition-transform\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-white text-xs font-bold\",\n                            children: \"1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 100,\n                        scale: 0.8\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: 100,\n                        scale: 0.8\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    },\n                    className: \"fixed bottom-6 right-6 z-50 w-96 h-[500px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-4 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold\",\n                                                    children: \"AMPD Dev-IQ Assistant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/80 text-sm\",\n                                                    children: \"Online now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                    \"aria-label\": \"Close chat\",\n                                    title: \"Close chat\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-4 overflow-y-auto space-y-4\",\n                            children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex items-start space-x-2 max-w-[80%] ${message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${message.sender === 'user' ? 'bg-blue-600' : 'bg-gray-200'}`,\n                                                children: message.sender === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 text-gray-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `rounded-2xl p-3 ${message.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: message.text\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-xs mt-1 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                                                        children: message.timestamp\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, message.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined),\n                        messages.length === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 pb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mb-2\",\n                                    children: \"Quick replies:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: quickReplies.map((reply, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>handleQuickReply(reply),\n                                            className: \"text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full transition-colors\",\n                                            title: `Quick reply: ${reply}`,\n                                            children: reply\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: inputMessage,\n                                            onChange: (e)=>setInputMessage(e.target.value),\n                                            onKeyPress: (e)=>e.key === 'Enter' && handleSendMessage(),\n                                            placeholder: \"Type your message...\",\n                                            className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: handleSendMessage,\n                                            disabled: !inputMessage.trim(),\n                                            className: \"w-10 h-10 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 rounded-lg flex items-center justify-center transition-colors\",\n                                            \"aria-label\": \"Send message\",\n                                            title: \"Send message\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_ComputerDesktopIcon_PaperAirplaneIcon_UserIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Powered by AMPD Dev-IQ AI • We typically respond within 2 hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\LiveChat.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LiveChat);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/LiveChat.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AdvancedAnimations */ \"(ssr)/./components/AdvancedAnimations.tsx\");\n/* harmony import */ var _MovingGraphics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MovingGraphics */ \"(ssr)/./components/MovingGraphics.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst ModernSinglePage = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernSinglePage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ModernSinglePage.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ModernSinglePage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernSinglePage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ModernSinglePage.useEffect\"];\n        }\n    }[\"ModernSinglePage.useEffect\"], []);\n    const services = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: 'Web Development',\n            description: 'Modern, responsive websites built with cutting-edge technologies',\n            features: [\n                'React/Next.js',\n                'TypeScript',\n                'Tailwind CSS',\n                'Progressive Web Apps'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Mobile Apps',\n            description: 'Native and cross-platform mobile applications',\n            features: [\n                'React Native',\n                'Flutter',\n                'iOS/Android',\n                'App Store Optimization'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: 'Cloud Solutions',\n            description: 'Scalable cloud infrastructure and deployment',\n            features: [\n                'AWS/Azure',\n                'Docker',\n                'Kubernetes',\n                'CI/CD Pipelines'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'AI/ML Solutions',\n            description: 'Intelligent systems and machine learning integration',\n            features: [\n                'TensorFlow',\n                'PyTorch',\n                'Computer Vision',\n                'NLP'\n            ]\n        }\n    ];\n    const stats = [\n        {\n            number: '500+',\n            label: 'Projects Delivered'\n        },\n        {\n            number: '50+',\n            label: 'Happy Clients'\n        },\n        {\n            number: '5+',\n            label: 'Years Experience'\n        },\n        {\n            number: '24/7',\n            label: 'Support'\n        }\n    ];\n    const testimonials = [\n        {\n            name: 'Sarah Johnson',\n            company: 'TechCorp',\n            text: 'AMPD Dev-IQ transformed our digital presence completely. Outstanding work!',\n            rating: 5\n        },\n        {\n            name: 'Michael Chen',\n            company: 'StartupXYZ',\n            text: 'Professional, efficient, and delivered beyond our expectations.',\n            rating: 5\n        }\n    ];\n    const handleContactSubmit = (e)=>{\n        e.preventDefault();\n        const formData = new FormData(e.target);\n        const message = `Hi! I'm ${formData.get('name')} from ${formData.get('company')}.\n\nEmail: ${formData.get('email')}\nBudget: ${formData.get('budget')}\n\nProject Details:\n${formData.get('message')}\n\nI'd like to discuss this project with you.`;\n        const whatsappUrl = `https://wa.me/***********?text=${encodeURIComponent(message)}`;\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.FloatingParticles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.MorphingBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.MovingShapes, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.CursorFollower, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.AnimatedGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.FloatingTechIcons, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.ParticleSystem, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 150,\n                color: \"#22d3ee\",\n                className: \"top-20 right-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 100,\n                color: \"#a855f7\",\n                className: \"bottom-40 left-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 80,\n                color: \"#f97316\",\n                className: \"top-1/2 left-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\",\n                                alt: \"Software Development Background\",\n                                className: \"w-full h-full object-cover opacity-15\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900/98 via-purple-900/95 to-slate-900/98\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                            alt: \"Tech Animation\",\n                            className: \"w-full h-full object-cover mix-blend-overlay\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://i.pinimg.com/originals/36/e4/d0/36e4d0b856694fc471344b644a1dd6e4.gif\",\n                            alt: \"Tech Animation\",\n                            className: \"w-full h-full object-cover mix-blend-screen\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                        initial: {\n                            y: -100\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-8 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/ampd-logo.png\",\n                                                        alt: \"AMPD Dev-IQ\",\n                                                        className: \"w-12 h-12 object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-black text-white\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-cyan-400\",\n                                                            children: \"Smart Software. Limitless Potential.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#services\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#portfolio\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                            className: \"lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: \"lg:hidden mt-4 py-4 border-t border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#services\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#portfolio\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"min-h-screen flex items-center justify-center px-8 pt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-7 h-7 text-cyan-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg neon-glow\",\n                                            children: \"Trusted by 500+ Companies Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.InteractiveBlob, {\n                                            className: \"absolute inset-0 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"SMART SOFTWARE.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.GlitchText, {\n                                            text: \"LIMITLESS\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"POTENTIAL.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"We transform your \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyan-400 font-semibold\",\n                                            children: \"business ideas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        \" into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400 font-semibold\",\n                                            children: \" powerful digital solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" that drive growth and innovation.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"flex flex-col md:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                            onClick: ()=>document.getElementById('contact')?.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                }),\n                                            className: \"text-xl px-12 py-6 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Start Your Project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.a, {\n                                            href: \"#about\",\n                                            className: \"group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                boxShadow: \"0 0 30px rgba(34, 211, 238, 0.5)\"\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Watch Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 font-semibold\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"services\",\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Team Collaboration\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR SERVICES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Comprehensive digital solutions to transform your business with cutting-edge technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-10 lg:gap-12\",\n                                            children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 opacity-5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-600/20 rounded-3xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-20 h-20 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-2xl shadow-cyan-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                            className: \"w-10 h-10 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 419,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-3xl lg:text-4xl font-bold text-white mb-6 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                        children: service.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xl text-gray-300 mb-8 leading-relaxed group-hover:text-gray-200 transition-colors duration-300\",\n                                                                        children: service.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"space-y-4\",\n                                                                        children: service.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-center text-lg text-gray-400 group-hover:text-gray-300 transition-colors duration-300\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"w-6 h-6 text-cyan-400 mr-4 group-hover:text-cyan-300 transition-colors duration-300\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                        lineNumber: 434,\n                                                                                        columnNumber: 27\n                                                                                    }, undefined),\n                                                                                    feature\n                                                                                ]\n                                                                            }, featureIndex, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 25\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"about\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                        alt: \"Team Working\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-orange-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"ABOUT US\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 leading-relaxed\",\n                                                    children: [\n                                                        \"At \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyan-400 font-bold ampd-glow\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 22\n                                                        }, undefined),\n                                                        \", we believe that smart software has the power to unlock limitless potential in every business. Our mission is to amplify your success through intelligent solutions and innovative technology.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-cyan-400 mb-4 neon-glow\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"Client Satisfaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-purple-400 mb-4 neon-glow\",\n                                                                    children: \"100%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"On-Time Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                                    title: 'Innovation',\n                                                    desc: 'Cutting-edge solutions'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                    title: 'Quality',\n                                                    desc: 'Premium standards'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                                    title: 'Reliability',\n                                                    desc: 'Trusted delivery'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    title: 'Excellence',\n                                                    desc: 'Outstanding results'\n                                                }\n                                            ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-32 px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mb-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-5xl md:text-7xl font-black font-poppins mb-8 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent\",\n                                        children: \"CLIENT TESTIMONIALS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        ...Array(testimonial.rating)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-5 h-5 text-yellow-400 fill-current\"\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-200 text-lg mb-6 italic\",\n                                                    children: [\n                                                        '\"',\n                                                        testimonial.text,\n                                                        '\"'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-white\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-cyan-400\",\n                                                            children: testimonial.company\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 515,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"contact\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80\",\n                                        alt: \"Contact Background\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 564,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow\",\n                                                children: \"GET IN TOUCH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                children: \"Ready to transform your business? Let's discuss your project and unlock your potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                        title: 'Email Us',\n                                                        details: '<EMAIL>',\n                                                        action: ()=>window.open('mailto:<EMAIL>', '_blank')\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                                        title: 'WhatsApp Us',\n                                                        details: '+27 79 448 4159',\n                                                        action: ()=>window.open('https://wa.me/***********', '_blank')\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        title: 'Based In',\n                                                        details: 'Cape Town, South Africa'\n                                                    }\n                                                ].map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: contact.action,\n                                                        className: `enhanced-card p-8 flex items-center space-x-6 ${contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-white text-xl mb-2\",\n                                                                        children: contact.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 text-lg\",\n                                                                        children: contact.details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"enhanced-card p-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleContactSubmit,\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    placeholder: \"Your Name\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 637,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    placeholder: \"Your Email\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"company\",\n                                                            placeholder: \"Company Name\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            title: \"Budget Range\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Select Budget Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Under R50,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Under R50,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 664,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R50,000 - R150,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R50,000 - R150,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R150,000 - R300,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R150,000 - R300,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R300,000 - R500,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R300,000 - R500,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R500,000+\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R500,000+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 658,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 6,\n                                                            placeholder: \"Tell us about your project...\",\n                                                            required: true,\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50\",\n                                                            children: \"Send Message via WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-16 px-8 border-t border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/ampd-logo.png\",\n                                                alt: \"AMPD Dev-IQ\",\n                                                className: \"w-8 h-8 object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-black text-white\",\n                                                    children: \"AMPD Dev-IQ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-cyan-400\",\n                                                    children: \"Smart Software. Limitless Potential.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"\\xa9 2024 Developed by AMPD Dev-IQ. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: \"Built with ❤️ and smart software for limitless potential.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSinglePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ModernSinglePage.tsx\n");

/***/ }),

/***/ "(ssr)/./components/MovingGraphics.tsx":
/*!***************************************!*\
  !*** ./components/MovingGraphics.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatedGrid: () => (/* binding */ AnimatedGrid),\n/* harmony export */   CursorFollower: () => (/* binding */ CursorFollower),\n/* harmony export */   FloatingTechIcons: () => (/* binding */ FloatingTechIcons),\n/* harmony export */   GlowingOrb: () => (/* binding */ GlowingOrb),\n/* harmony export */   HolographicCard: () => (/* binding */ HolographicCard),\n/* harmony export */   MorphingBlob: () => (/* binding */ MorphingBlob),\n/* harmony export */   MovingShapes: () => (/* binding */ MovingShapes),\n/* harmony export */   ParticleSystem: () => (/* binding */ ParticleSystem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-spring.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ MovingShapes,CursorFollower,AnimatedGrid,FloatingTechIcons,MorphingBlob,ParticleSystem,HolographicCard,GlowingOrb auto */ \n\n\n// Moving Geometric Shapes Component\nconst MovingShapes = ()=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    const y1 = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        -200\n    ]);\n    const y2 = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        200\n    ]);\n    const rotate = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        0,\n        360\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                style: {\n                    y: y1,\n                    rotate\n                },\n                className: \"absolute top-20 left-10 w-20 h-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-br from-cyan-400/30 to-blue-600/30 transform rotate-45 animate-morph\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                style: {\n                    y: y2\n                },\n                className: \"absolute top-40 right-20 w-16 h-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full bg-gradient-to-br from-purple-400/30 to-pink-600/30 rounded-full animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                style: {\n                    y: y1\n                },\n                className: \"absolute top-60 left-1/4 w-32 h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                style: {\n                    y: y2,\n                    rotate\n                },\n                className: \"absolute bottom-40 right-1/3 w-24 h-24 border-2 border-orange-400/40 rounded-lg animate-morph\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined);\n};\n// Interactive Cursor Follower\nconst CursorFollower = ()=>{\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const cursorX = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useMotionValue)(0);\n    const cursorY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_5__.useMotionValue)(0);\n    const springConfig = {\n        damping: 25,\n        stiffness: 700\n    };\n    const cursorXSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(cursorX, springConfig);\n    const cursorYSpring = (0,framer_motion__WEBPACK_IMPORTED_MODULE_6__.useSpring)(cursorY, springConfig);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CursorFollower.useEffect\": ()=>{\n            const handleMouseMove = {\n                \"CursorFollower.useEffect.handleMouseMove\": (e)=>{\n                    setMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                    cursorX.set(e.clientX - 16);\n                    cursorY.set(e.clientY - 16);\n                }\n            }[\"CursorFollower.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            return ({\n                \"CursorFollower.useEffect\": ()=>window.removeEventListener('mousemove', handleMouseMove)\n            })[\"CursorFollower.useEffect\"];\n        }\n    }[\"CursorFollower.useEffect\"], [\n        cursorX,\n        cursorY\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: \"fixed w-8 h-8 pointer-events-none z-50 mix-blend-difference\",\n        style: {\n            left: cursorXSpring,\n            top: cursorYSpring\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full bg-white rounded-full opacity-80\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n// Animated Background Grid\nconst AnimatedGrid = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none opacity-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            className: \"w-full h-full\",\n            style: {\n                backgroundImage: `\n            linear-gradient(rgba(34, 211, 238, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(34, 211, 238, 0.1) 1px, transparent 1px)\n          `,\n                backgroundSize: '50px 50px'\n            },\n            animate: {\n                backgroundPosition: [\n                    '0px 0px',\n                    '50px 50px'\n                ]\n            },\n            transition: {\n                duration: 20,\n                repeat: Infinity,\n                ease: \"linear\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n// Floating Tech Icons\nconst FloatingTechIcons = ()=>{\n    const icons = [\n        {\n            symbol: '</>',\n            color: 'text-cyan-400',\n            size: 'text-2xl'\n        },\n        {\n            symbol: '{}',\n            color: 'text-purple-400',\n            size: 'text-xl'\n        },\n        {\n            symbol: '<>',\n            color: 'text-orange-400',\n            size: 'text-lg'\n        },\n        {\n            symbol: '[]',\n            color: 'text-green-400',\n            size: 'text-xl'\n        },\n        {\n            symbol: '()',\n            color: 'text-pink-400',\n            size: 'text-lg'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n        children: icons.map((icon, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: `absolute ${icon.color} ${icon.size} font-mono font-bold`,\n                style: {\n                    left: `${Math.random() * 100}%`,\n                    top: `${Math.random() * 100}%`\n                },\n                animate: {\n                    y: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        -15,\n                        0\n                    ],\n                    rotate: [\n                        0,\n                        10,\n                        -10,\n                        0\n                    ],\n                    opacity: [\n                        0.3,\n                        0.8,\n                        0.3\n                    ]\n                },\n                transition: {\n                    duration: Math.random() * 8 + 6,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: Math.random() * 5\n                },\n                children: icon.symbol\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, undefined);\n};\n// Morphing Blob Background\nconst MorphingBlob = ({ className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `absolute inset-0 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            className: \"absolute top-1/4 left-1/4 w-96 h-96 opacity-20\",\n            animate: {\n                borderRadius: [\n                    \"60% 40% 30% 70% / 60% 30% 70% 40%\",\n                    \"70% 60% 70% 30% / 50% 60% 30% 60%\",\n                    \"100% 60% 60% 100% / 100% 100% 60% 60%\",\n                    \"60% 40% 30% 70% / 60% 30% 70% 40%\"\n                ],\n                background: [\n                    \"linear-gradient(45deg, #22d3ee, #a855f7)\",\n                    \"linear-gradient(45deg, #a855f7, #f97316)\",\n                    \"linear-gradient(45deg, #f97316, #10b981)\",\n                    \"linear-gradient(45deg, #10b981, #22d3ee)\"\n                ]\n            },\n            transition: {\n                duration: 8,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, undefined);\n};\n// Particle System\nconst ParticleSystem = ()=>{\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParticleSystem.useEffect\": ()=>{\n            const newParticles = Array.from({\n                length: 30\n            }, {\n                \"ParticleSystem.useEffect.newParticles\": (_, i)=>({\n                        id: i,\n                        x: Math.random() * window.innerWidth,\n                        y: Math.random() * window.innerHeight,\n                        size: Math.random() * 3 + 1,\n                        color: [\n                            '#22d3ee',\n                            '#a855f7',\n                            '#f97316',\n                            '#10b981'\n                        ][Math.floor(Math.random() * 4)],\n                        speed: Math.random() * 2 + 1\n                    })\n            }[\"ParticleSystem.useEffect.newParticles\"]);\n            setParticles(newParticles);\n        }\n    }[\"ParticleSystem.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n        children: particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                className: \"absolute rounded-full opacity-60\",\n                style: {\n                    width: `${particle.size}px`,\n                    height: `${particle.size}px`,\n                    backgroundColor: particle.color,\n                    boxShadow: `0 0 ${particle.size * 3}px ${particle.color}`\n                },\n                animate: {\n                    x: [\n                        particle.x,\n                        particle.x + 100,\n                        particle.x - 50,\n                        particle.x\n                    ],\n                    y: [\n                        particle.y,\n                        particle.y - 100,\n                        particle.y + 50,\n                        particle.y\n                    ],\n                    scale: [\n                        1,\n                        1.5,\n                        0.5,\n                        1\n                    ],\n                    opacity: [\n                        0.6,\n                        1,\n                        0.3,\n                        0.6\n                    ]\n                },\n                transition: {\n                    duration: particle.speed * 10,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: Math.random() * 5\n                }\n            }, particle.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, undefined);\n};\n// Holographic Card Effect\nconst HolographicCard = ({ children, className = \"\" })=>{\n    const [rotateX, setRotateX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [rotateY, setRotateY] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleMouseMove = (e)=>{\n        const card = e.currentTarget;\n        const rect = card.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        const rotateXValue = (e.clientY - centerY) / 10;\n        const rotateYValue = (centerX - e.clientX) / 10;\n        setRotateX(rotateXValue);\n        setRotateY(rotateYValue);\n    };\n    const handleMouseLeave = ()=>{\n        setRotateX(0);\n        setRotateY(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: `card-3d ${className}`,\n        style: {\n            transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`\n        },\n        onMouseMove: handleMouseMove,\n        onMouseLeave: handleMouseLeave,\n        transition: {\n            type: \"spring\",\n            stiffness: 300,\n            damping: 30\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, undefined);\n};\n// Glowing Orb\nconst GlowingOrb = ({ size = 100, color = \"#22d3ee\", className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        className: `absolute rounded-full ${className}`,\n        style: {\n            width: size,\n            height: size,\n            background: `radial-gradient(circle, ${color}40, ${color}20, transparent)`,\n            boxShadow: `0 0 ${size}px ${color}60`\n        },\n        animate: {\n            scale: [\n                1,\n                1.2,\n                1\n            ],\n            opacity: [\n                0.6,\n                1,\n                0.6\n            ]\n        },\n        transition: {\n            duration: 3,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\MovingGraphics.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/MovingGraphics.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/LiveChat.tsx */ \"(ssr)/./components/LiveChat.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ModernSinglePage.tsx */ \"(ssr)/./components/ModernSinglePage.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q01heVJha2dhbWElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDRGV2SVElNUMlNUNkZXZpcS1hZ2VuY3klNUMlNUNjb21wb25lbnRzJTVDJTVDTGl2ZUNoYXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNNYXlSYWtnYW1hJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0RldklRJTVDJTVDZGV2aXEtYWdlbmN5JTVDJTVDY29tcG9uZW50cyU1QyU1Q01vZGVyblNpbmdsZVBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1LO0FBQ25LO0FBQ0EsOEtBQTJLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcTWF5UmFrZ2FtYVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxEZXZJUVxcXFxkZXZpcS1hZ2VuY3lcXFxcY29tcG9uZW50c1xcXFxMaXZlQ2hhdC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxNYXlSYWtnYW1hXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXERldklRXFxcXGRldmlxLWFnZW5jeVxcXFxjb21wb25lbnRzXFxcXE1vZGVyblNpbmdsZVBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CLiveChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Ccomponents%5C%5CModernSinglePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CMayRakgama%5C%5CDocuments%5C%5Caugment-projects%5C%5CDevIQ%5C%5Cdeviq-agency%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CMayRakgama%5CDocuments%5Caugment-projects%5CDevIQ%5Cdeviq-agency&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();