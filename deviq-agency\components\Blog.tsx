'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { CalendarIcon, ClockIcon, UserIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTag, setActiveTag] = useState('All');

  const blogPosts = [
    {
      id: 1,
      title: 'The Future of Web Development: Trends to Watch in 2024',
      excerpt: 'Explore the latest trends shaping the future of web development, from AI integration to progressive web apps.',
      author: '<PERSON>',
      date: '2024-01-15',
      readTime: '5 min read',
      image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&h=400&fit=crop',
      tags: ['Web Development', 'Trends', 'AI'],
      category: 'Technology'
    },
    {
      id: 2,
      title: 'Building Scalable Microservices with Node.js and Docker',
      excerpt: 'Learn how to architect and deploy scalable microservices using modern containerization technologies.',
      author: '<PERSON>',
      date: '2024-01-10',
      readTime: '8 min read',
      image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop',
      tags: ['Node.js', 'Docker', 'Microservices'],
      category: 'Development'
    },
    {
      id: 3,
      title: 'AI-Powered User Experience: The Next Frontier',
      excerpt: 'Discover how artificial intelligence is revolutionizing user experience design and development.',
      author: 'Mike Chen',
      date: '2024-01-05',
      readTime: '6 min read',
      image: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?w=600&h=400&fit=crop',
      tags: ['AI', 'UX Design', 'Machine Learning'],
      category: 'Design'
    },
    {
      id: 4,
      title: 'Cloud Security Best Practices for Modern Applications',
      excerpt: 'Essential security practices every developer should know when building cloud-native applications.',
      author: 'Emily Davis',
      date: '2024-01-01',
      readTime: '7 min read',
      image: 'https://images.unsplash.com/photo-1563986768609-322da13575f3?w=600&h=400&fit=crop',
      tags: ['Cloud', 'Security', 'DevOps'],
      category: 'Security'
    },
    {
      id: 5,
      title: 'React Performance Optimization: Advanced Techniques',
      excerpt: 'Deep dive into advanced React optimization techniques to build lightning-fast applications.',
      author: 'David Wilson',
      date: '2023-12-28',
      readTime: '10 min read',
      image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=600&h=400&fit=crop',
      tags: ['React', 'Performance', 'JavaScript'],
      category: 'Development'
    },
    {
      id: 6,
      title: 'The Rise of Low-Code Development Platforms',
      excerpt: 'Exploring how low-code platforms are changing the software development landscape.',
      author: 'Lisa Anderson',
      date: '2023-12-25',
      readTime: '4 min read',
      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop',
      tags: ['Low-Code', 'Development', 'Productivity'],
      category: 'Technology'
    }
  ];

  const tags = ['All', 'Web Development', 'AI', 'React', 'Node.js', 'Cloud', 'Security', 'Design'];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTag = activeTag === 'All' || post.tags.includes(activeTag);
    return matchesSearch && matchesTag;
  });

  return (
    <section id="blog" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-red-500/20 to-pink-500/20 backdrop-blur-md border border-red-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-red-500/20">
            <span className="text-red-400 font-semibold text-2xl">Latest Insights</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-red-400 via-pink-500 to-purple-600 bg-clip-text text-transparent drop-shadow-2xl">
              LATEST INSIGHTS
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            Stay updated with the <span className="text-red-400 font-semibold">latest trends</span>, best practices, and insights from the
            <span className="text-pink-400 font-semibold"> world of software development</span>.
          </p>
        </motion.div>

        {/* Enhanced Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-16"
        >
          {/* Enhanced Search Bar */}
          <div className="relative max-w-2xl mx-auto mb-12">
            <MagnifyingGlassIcon className="absolute left-6 top-1/2 transform -translate-y-1/2 w-8 h-8 text-gray-400" />
            <input
              type="text"
              placeholder="Search articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-16 pr-6 py-6 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-red-500 focus:border-red-400 transition-all text-white text-xl placeholder-gray-300 backdrop-blur-md"
            />
          </div>

          {/* Enhanced Tag Filter */}
          <div className="flex flex-wrap justify-center gap-4">
            {tags.map((tag) => (
              <button
                key={tag}
                type="button"
                onClick={() => setActiveTag(tag)}
                className={`px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 backdrop-blur-md shadow-xl ${
                  activeTag === tag
                    ? 'bg-gradient-to-r from-red-500 to-pink-600 text-white border-2 border-red-400/50 shadow-red-500/50'
                    : 'bg-gradient-to-r from-white/10 to-white/5 text-white border-2 border-white/30 hover:bg-gradient-to-r hover:from-red-500/20 hover:to-pink-500/20 hover:border-red-400/50'
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Blog Posts Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredPosts.map((post, index) => (
            <motion.article
              key={post.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl overflow-hidden shadow-2xl hover:shadow-red-500/30 transition-all duration-500 group cursor-pointer transform hover:-translate-y-4 hover:scale-105"
            >
              {/* Enhanced Featured Image */}
              <div className="relative overflow-hidden">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute top-6 left-6">
                  <span className={`text-lg font-bold px-4 py-2 rounded-2xl ${
                    post.category === 'Technology' ? 'text-red-400 bg-red-500/20 border border-red-400/30' :
                    post.category === 'Development' ? 'text-pink-400 bg-pink-500/20 border border-pink-400/30' :
                    post.category === 'Design' ? 'text-purple-400 bg-purple-500/20 border border-purple-400/30' :
                    post.category === 'Security' ? 'text-blue-400 bg-blue-500/20 border border-blue-400/30' :
                    'text-cyan-400 bg-cyan-500/20 border border-cyan-400/30'
                  }`}>
                    {post.category}
                  </span>
                </div>
              </div>

              {/* Enhanced Content */}
              <div className="p-8">
                {/* Enhanced Meta Info */}
                <div className="flex items-center text-lg text-gray-300 mb-6 space-x-6">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-5 h-5" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="w-5 h-5" />
                    <span>{new Date(post.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="w-5 h-5" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                {/* Enhanced Title */}
                <h3 className="text-3xl font-bold font-poppins mb-6 text-white group-hover:text-red-400 transition-colors line-clamp-2 neon-glow">
                  {post.title}
                </h3>

                {/* Enhanced Excerpt */}
                <p className="text-xl text-gray-200 mb-6 line-clamp-3 leading-relaxed">
                  {post.excerpt}
                </p>

                {/* Enhanced Tags */}
                <div className="flex flex-wrap gap-3 mb-6">
                  {post.tags.slice(0, 3).map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="text-sm bg-gradient-to-r from-white/20 to-white/10 text-white px-3 py-2 rounded-xl border border-white/20 font-semibold"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                {/* Enhanced Read More */}
                <button type="button" className="text-red-400 font-bold text-xl hover:text-red-300 transition-colors flex items-center space-x-2">
                  <span>Read More</span>
                  <span>→</span>
                </button>
              </div>
            </motion.article>
          ))}
        </motion.div>

        {/* Enhanced No Results */}
        {filteredPosts.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-16"
          >
            <p className="text-gray-200 text-3xl font-bold">
              No articles found matching your criteria.
            </p>
          </motion.div>
        )}

        {/* Enhanced CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <p className="text-3xl md:text-4xl text-gray-200 mb-12 leading-relaxed">
            Want to stay updated with our <span className="text-red-400 font-semibold">latest insights</span>?
          </p>
          <button type="button" className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-400 hover:to-pink-500 text-white font-bold text-3xl px-16 py-8 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-red-500/50 border-2 border-red-400/30">
            SUBSCRIBE TO NEWSLETTER
          </button>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Blog;
