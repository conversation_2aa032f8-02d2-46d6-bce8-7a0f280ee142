'use client';

import { motion, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';
import { useRef, useEffect, useState } from 'react';

// Moving Geometric Shapes Component
export const MovingShapes = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const y1 = useTransform(scrollYProgress, [0, 1], [0, -200]);
  const y2 = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360]);

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Floating Triangles */}
      <motion.div
        style={{ y: y1, rotate }}
        className="absolute top-20 left-10 w-20 h-20"
      >
        <div className="w-full h-full bg-gradient-to-br from-cyan-400/30 to-blue-600/30 transform rotate-45 animate-morph"></div>
      </motion.div>
      
      <motion.div
        style={{ y: y2 }}
        className="absolute top-40 right-20 w-16 h-16"
      >
        <div className="w-full h-full bg-gradient-to-br from-purple-400/30 to-pink-600/30 rounded-full animate-pulse"></div>
      </motion.div>

      {/* Moving Lines */}
      <motion.div
        style={{ y: y1 }}
        className="absolute top-60 left-1/4 w-32 h-1 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse"
      ></motion.div>

      <motion.div
        style={{ y: y2, rotate }}
        className="absolute bottom-40 right-1/3 w-24 h-24 border-2 border-orange-400/40 rounded-lg animate-morph"
      ></motion.div>
    </div>
  );
};

// Interactive Cursor Follower
export const CursorFollower = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cursorX = useMotionValue(0);
  const cursorY = useMotionValue(0);
  
  const springConfig = { damping: 25, stiffness: 700 };
  const cursorXSpring = useSpring(cursorX, springConfig);
  const cursorYSpring = useSpring(cursorY, springConfig);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      cursorX.set(e.clientX - 16);
      cursorY.set(e.clientY - 16);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [cursorX, cursorY]);

  return (
    <motion.div
      className="fixed w-8 h-8 pointer-events-none z-50 mix-blend-difference"
      style={{
        left: cursorXSpring,
        top: cursorYSpring,
      }}
    >
      <div className="w-full h-full bg-white rounded-full opacity-80"></div>
    </motion.div>
  );
};

// Animated Background Grid
export const AnimatedGrid = () => {
  return (
    <div className="fixed inset-0 pointer-events-none opacity-20">
      <motion.div
        className="w-full h-full"
        style={{
          backgroundImage: `
            linear-gradient(rgba(34, 211, 238, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 211, 238, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
        }}
        animate={{
          backgroundPosition: ['0px 0px', '50px 50px'],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear"
        }}
      />
    </div>
  );
};

// Floating Tech Icons
export const FloatingTechIcons = () => {
  const icons = [
    { symbol: '</>', color: 'text-cyan-400', size: 'text-2xl' },
    { symbol: '{}', color: 'text-purple-400', size: 'text-xl' },
    { symbol: '<>', color: 'text-orange-400', size: 'text-lg' },
    { symbol: '[]', color: 'text-green-400', size: 'text-xl' },
    { symbol: '()', color: 'text-pink-400', size: 'text-lg' },
  ];

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {icons.map((icon, index) => (
        <motion.div
          key={index}
          className={`absolute ${icon.color} ${icon.size} font-mono font-bold`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, -15, 0],
            rotate: [0, 10, -10, 0],
            opacity: [0.3, 0.8, 0.3],
          }}
          transition={{
            duration: Math.random() * 8 + 6,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        >
          {icon.symbol}
        </motion.div>
      ))}
    </div>
  );
};

// Morphing Blob Background
export const MorphingBlob = ({ className = "" }: { className?: string }) => {
  return (
    <div className={`absolute inset-0 ${className}`}>
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 opacity-20"
        animate={{
          borderRadius: [
            "60% 40% 30% 70% / 60% 30% 70% 40%",
            "70% 60% 70% 30% / 50% 60% 30% 60%",
            "100% 60% 60% 100% / 100% 100% 60% 60%",
            "60% 40% 30% 70% / 60% 30% 70% 40%"
          ],
          background: [
            "linear-gradient(45deg, #22d3ee, #a855f7)",
            "linear-gradient(45deg, #a855f7, #f97316)",
            "linear-gradient(45deg, #f97316, #10b981)",
            "linear-gradient(45deg, #10b981, #22d3ee)"
          ]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  );
};

// Particle System
export const ParticleSystem = () => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    speed: number;
  }>>([]);

  useEffect(() => {
    const newParticles = Array.from({ length: 30 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      size: Math.random() * 3 + 1,
      color: ['#22d3ee', '#a855f7', '#f97316', '#10b981'][Math.floor(Math.random() * 4)],
      speed: Math.random() * 2 + 1
    }));
    setParticles(newParticles);
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full opacity-60"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            boxShadow: `0 0 ${particle.size * 3}px ${particle.color}`,
          }}
          animate={{
            x: [particle.x, particle.x + 100, particle.x - 50, particle.x],
            y: [particle.y, particle.y - 100, particle.y + 50, particle.y],
            scale: [1, 1.5, 0.5, 1],
            opacity: [0.6, 1, 0.3, 0.6],
          }}
          transition={{
            duration: particle.speed * 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 5,
          }}
        />
      ))}
    </div>
  );
};

// Holographic Card Effect
export const HolographicCard = ({ 
  children, 
  className = "" 
}: { 
  children: React.ReactNode;
  className?: string;
}) => {
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const card = e.currentTarget;
    const rect = card.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const rotateXValue = (e.clientY - centerY) / 10;
    const rotateYValue = (centerX - e.clientX) / 10;

    setRotateX(rotateXValue);
    setRotateY(rotateYValue);
  };

  const handleMouseLeave = () => {
    setRotateX(0);
    setRotateY(0);
  };

  return (
    <motion.div
      className={`card-3d ${className}`}
      style={{
        transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`,
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {children}
    </motion.div>
  );
};

// Glowing Orb
export const GlowingOrb = ({ 
  size = 100, 
  color = "#22d3ee",
  className = "" 
}: { 
  size?: number;
  color?: string;
  className?: string;
}) => {
  return (
    <motion.div
      className={`absolute rounded-full ${className}`}
      style={{
        width: size,
        height: size,
        background: `radial-gradient(circle, ${color}40, ${color}20, transparent)`,
        boxShadow: `0 0 ${size}px ${color}60`,
      }}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.6, 1, 0.6],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
};
