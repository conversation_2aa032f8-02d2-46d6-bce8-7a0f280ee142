'use client';

import { motion } from 'framer-motion';
import { 
  MagnifyingGlassIcon,
  PencilSquareIcon,
  CodeBracketIcon,
  RocketLaunchIcon,
  WrenchScrewdriverIcon
} from '@heroicons/react/24/outline';

const Process = () => {
  const steps = [
    {
      icon: MagnifyingGlassIcon,
      title: 'Discovery',
      description: 'We dive deep into understanding your business goals, target audience, and technical requirements.',
      details: [
        'Stakeholder interviews',
        'Market research',
        'Technical feasibility analysis',
        'Project scope definition'
      ],
      color: 'from-blue-500 to-blue-600',
      duration: '1-2 weeks'
    },
    {
      icon: PencilSquareIcon,
      title: 'Design',
      description: 'Our design team creates intuitive user experiences and beautiful interfaces that align with your brand.',
      details: [
        'User experience design',
        'Visual design & branding',
        'Interactive prototypes',
        'Design system creation'
      ],
      color: 'from-purple-500 to-purple-600',
      duration: '2-3 weeks'
    },
    {
      icon: CodeBracketIcon,
      title: 'Develop',
      description: 'Our developers bring designs to life using cutting-edge technologies and best practices.',
      details: [
        'Agile development methodology',
        'Code reviews & testing',
        'Regular progress updates',
        'Quality assurance'
      ],
      color: 'from-green-500 to-green-600',
      duration: '4-12 weeks'
    },
    {
      icon: RocketLaunchIcon,
      title: 'Deploy',
      description: 'We ensure smooth deployment with comprehensive testing and performance optimization.',
      details: [
        'Production deployment',
        'Performance optimization',
        'Security implementation',
        'Go-live support'
      ],
      color: 'from-orange-500 to-orange-600',
      duration: '1 week'
    },
    {
      icon: WrenchScrewdriverIcon,
      title: 'Support',
      description: 'Ongoing maintenance, updates, and support to ensure your solution continues to perform optimally.',
      details: [
        '24/7 monitoring',
        'Regular updates',
        'Bug fixes & improvements',
        'Technical support'
      ],
      color: 'from-pink-500 to-pink-600',
      duration: 'Ongoing'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="process" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-orange-500/20 to-purple-500/20 backdrop-blur-md border border-orange-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-orange-500/20">
            <span className="text-orange-400 font-semibold text-2xl">Our Process</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-orange-400 via-red-500 to-purple-600 bg-clip-text text-transparent drop-shadow-2xl">
              HOW WE WORK
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            Our <span className="text-orange-400 font-semibold">proven process</span> ensures successful project delivery from
            <span className="text-purple-400 font-semibold"> concept to launch and beyond</span>.
          </p>
        </motion.div>

        {/* Ring-Connected Roadmap */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="relative"
        >
          {/* Desktop Ring Layout */}
          <div className="hidden lg:block relative">
            {/* Central Hub */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 bg-gradient-to-br from-orange-500/20 to-purple-600/20 backdrop-blur-xl border-4 border-orange-400/40 rounded-full flex items-center justify-center shadow-2xl z-20">
              <div className="text-center">
                <div className="text-4xl font-black text-white mb-2 neon-glow">OUR</div>
                <div className="text-3xl font-black text-orange-400 neon-glow">PROCESS</div>
              </div>
            </div>

            {/* Ring Container */}
            <div className="relative w-[800px] h-[800px] mx-auto">
              {steps.map((step, index) => {
                // Calculate position on circle
                const angle = (index * 72) - 90; // 72 degrees apart (360/5), start from top
                const radius = 300;
                const x = Math.cos(angle * Math.PI / 180) * radius;
                const y = Math.sin(angle * Math.PI / 180) * radius;

                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="absolute"
                    style={{
                      left: `calc(50% + ${x}px - 200px)`,
                      top: `calc(50% + ${y}px - 150px)`,
                    }}
                  >
                    {/* Connecting Line to Center */}
                    <div
                      className="absolute w-1 bg-gradient-to-r from-orange-400/60 to-purple-400/60 shadow-lg z-10"
                      style={{
                        height: `${radius - 96}px`,
                        left: '199px',
                        top: '150px',
                        transformOrigin: 'top center',
                        transform: `rotate(${angle + 90}deg)`,
                      }}
                    ></div>

                    {/* Step Content Card */}
                    <div className="w-[400px] bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl border-2 border-white/40 rounded-3xl p-8 shadow-2xl hover:shadow-orange-500/40 transition-all duration-700 transform hover:-translate-y-4 hover:scale-105 hover:border-orange-400/60 z-20 relative">
                      {/* Step Number and Icon */}
                      <div className="flex items-center gap-6 mb-6">
                        <div className={`w-20 h-20 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center shadow-2xl border-4 border-white/20`}>
                          <span className="text-white font-bold text-2xl">{index + 1}</span>
                        </div>
                        <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center shadow-xl`}>
                          <step.icon className="w-8 h-8 text-white" />
                        </div>
                      </div>

                      {/* Step Title and Duration */}
                      <div className="mb-6">
                        <h3 className="text-3xl font-black font-poppins text-white neon-glow mb-2">
                          {step.title.toUpperCase()}
                        </h3>
                        <span className="text-lg text-orange-400 font-bold bg-orange-400/10 px-4 py-2 rounded-full">
                          {step.duration}
                        </span>
                      </div>

                      {/* Description */}
                      <p className="text-xl text-gray-200 mb-6 leading-relaxed">
                        {step.description}
                      </p>

                      {/* Details List */}
                      <ul className="space-y-3">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center text-lg text-gray-300">
                            <div className={`w-3 h-3 bg-gradient-to-r ${step.color} rounded-full mr-3 flex-shrink-0 shadow-lg`}></div>
                            {detail}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Mobile/Tablet Vertical Layout */}
          <div className="lg:hidden space-y-12">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative"
              >
                {/* Connecting Line */}
                {index < steps.length - 1 && (
                  <div className="absolute left-10 top-32 w-1 h-12 bg-gradient-to-b from-orange-400/60 to-purple-400/60 rounded-full shadow-lg z-10"></div>
                )}

                <div className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl border-2 border-white/40 rounded-3xl p-8 shadow-2xl hover:shadow-orange-500/40 transition-all duration-700 transform hover:-translate-y-4 hover:scale-105">
                  {/* Step Header */}
                  <div className="flex items-center gap-6 mb-6">
                    <div className={`w-20 h-20 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center shadow-2xl border-4 border-white/20`}>
                      <span className="text-white font-bold text-2xl">{index + 1}</span>
                    </div>
                    <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center shadow-xl`}>
                      <step.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl md:text-3xl font-black font-poppins text-white neon-glow">
                        {step.title.toUpperCase()}
                      </h3>
                      <span className="text-lg text-orange-400 font-bold">
                        {step.duration}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-xl text-gray-200 mb-6 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Details */}
                  <ul className="space-y-3">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center text-lg text-gray-300">
                        <div className={`w-3 h-3 bg-gradient-to-r ${step.color} rounded-full mr-3 flex-shrink-0 shadow-lg`}></div>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Process Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-24 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-12 md:p-16 shadow-2xl"
        >
          <div className="text-center mb-16">
            <h3 className="text-giant font-black font-poppins mb-8 text-white neon-glow">
              WHY OUR PROCESS WORKS
            </h3>
            <p className="text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
              Our <span className="text-orange-400 font-semibold">structured approach</span> ensures transparency, quality, and
              <span className="text-purple-400 font-semibold"> successful outcomes</span> for every project.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="text-center bg-gradient-to-br from-green-500/10 to-cyan-500/10 backdrop-blur-md p-8 rounded-3xl border border-green-400/20 shadow-xl">
              <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-cyan-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <span className="text-white font-bold text-4xl">✓</span>
              </div>
              <h4 className="text-3xl font-bold font-poppins mb-4 text-white neon-glow">
                TRANSPARENT COMMUNICATION
              </h4>
              <p className="text-xl text-gray-200 leading-relaxed">
                Regular updates and clear communication throughout the entire project lifecycle.
              </p>
            </div>

            <div className="text-center bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-md p-8 rounded-3xl border border-blue-400/20 shadow-xl">
              <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <span className="text-white font-bold text-4xl">⚡</span>
              </div>
              <h4 className="text-3xl font-bold font-poppins mb-4 text-white neon-glow">
                AGILE METHODOLOGY
              </h4>
              <p className="text-xl text-gray-200 leading-relaxed">
                Flexible development approach that adapts to changing requirements and feedback.
              </p>
            </div>

            <div className="text-center bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-md p-8 rounded-3xl border border-purple-400/20 shadow-xl">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                <span className="text-white font-bold text-4xl">🎯</span>
              </div>
              <h4 className="text-3xl font-bold font-poppins mb-4 text-white neon-glow">
                QUALITY ASSURANCE
              </h4>
              <p className="text-xl text-gray-200 leading-relaxed">
                Rigorous testing and quality checks to ensure bug-free, high-performance solutions.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Enhanced CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <p className="text-3xl md:text-4xl text-gray-200 mb-12 leading-relaxed">
            Ready to start your project with our <span className="text-orange-400 font-semibold">proven process</span>?
          </p>
          <button type="button" className="bg-gradient-to-r from-orange-500 to-purple-600 hover:from-orange-400 hover:to-purple-500 text-white font-bold text-3xl px-16 py-8 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-orange-500/50 border-2 border-orange-400/30">
            LET'S GET STARTED
          </button>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Process;
