'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: 'Home', href: '#home' },
    { name: 'Services', href: '#services' },
    { name: 'About', href: '#about' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Process', href: '#process' },
    { name: 'Pricing', href: '#pricing-calculator' },
    { name: 'Blog', href: '#blog' },
    { name: 'Contact', href: '#contact' },
  ];

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl border-b border-white/10'
          : 'bg-transparent'
      }`}
    >
      <nav className="container mx-auto px-8 py-6">
        <div className="flex items-center justify-between">
          {/* Enhanced Logo with Custom Image - Much Larger */}
          <Link href="/" className="flex items-center space-x-6 group">
            {/* Logo Image Container - Significantly Larger */}
            <div className="relative w-24 h-24 lg:w-28 lg:h-28 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-3xl flex items-center justify-center shadow-2xl shadow-cyan-500/30 backdrop-blur-xl border-2 border-cyan-400/40 overflow-hidden group-hover:scale-105 transition-all duration-300">
              <img
                src="/ampd-logo.png"
                alt="AMPD Dev-IQ Logo"
                className="w-18 h-18 lg:w-20 lg:h-20 object-contain filter brightness-110 contrast-110"
                style={{
                  filter: 'drop-shadow(0 0 8px rgba(34, 211, 238, 0.5))'
                }}
                onError={(e) => {
                  // Fallback to a styled letter if logo fails to load
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              <div className="w-18 h-18 lg:w-20 lg:h-20 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl hidden">
                <span className="text-white font-black text-3xl lg:text-4xl">A</span>
              </div>
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-purple-600/10 animate-pulse"></div>
            </div>

            {/* Brand Text - Much Larger */}
            <div className="flex flex-col">
              <div className="text-4xl lg:text-5xl xl:text-6xl font-black font-poppins leading-none">
                <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                  AMPD
                </span>
                <span className="text-white ml-1">Dev-IQ</span>
              </div>
              <p className="text-cyan-400 text-lg lg:text-xl font-semibold tracking-wide opacity-90">
                Smart Software. Limitless Potential.
              </p>
            </div>
          </Link>

          {/* Desktop Navigation - Larger Text */}
          <div className="hidden lg:flex items-center space-x-10 xl:space-x-12">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-white/80 hover:text-white transition-colors duration-300 font-medium relative group text-lg xl:text-xl"
              >
                {item.name}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            ))}
          </div>

          {/* CTA Button - Much Larger */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link
              href="#contact"
              className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-8 py-4 lg:px-10 lg:py-5 rounded-xl text-lg lg:text-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-cyan-500/25"
            >
              Book Intro Call
            </Link>
          </div>

          {/* Mobile Menu Button - Larger */}
          <button
            type="button"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-3 rounded-xl hover:bg-white/10 transition-colors"
          >
            {isMenuOpen ? (
              <XMarkIcon className="w-8 h-8 text-white" />
            ) : (
              <Bars3Icon className="w-8 h-8 text-white" />
            )}
          </button>
        </div>

        {/* Mobile Menu - Larger Text and Spacing */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden mt-6 py-6 border-t border-white/20"
          >
            <div className="flex flex-col space-y-6">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-white/80 hover:text-white transition-colors duration-300 font-medium py-3 text-xl"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <Link
                href="#contact"
                className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 mt-6 text-center text-lg"
                onClick={() => setIsMenuOpen(false)}
              >
                Book Intro Call
              </Link>
            </div>
          </motion.div>
        )}
      </nav>
    </motion.header>
  );
};

export default Header;
