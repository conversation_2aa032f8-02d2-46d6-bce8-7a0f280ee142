'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { 
  CalculatorIcon,
  CheckIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

const PricingCalculator = () => {
  const [projectType, setProjectType] = useState('');
  const [complexity, setComplexity] = useState('');
  const [features, setFeatures] = useState<string[]>([]);
  const [timeline, setTimeline] = useState('');

  const projectTypes = [
    { id: 'website', name: 'Website/Web App', basePrice: 50000 },
    { id: 'mobile', name: 'Mobile App', basePrice: 80000 },
    { id: 'enterprise', name: 'Enterprise Software', basePrice: 150000 },
    { id: 'ecommerce', name: 'E-commerce Platform', basePrice: 100000 },
    { id: 'ai', name: 'AI/ML Solution', basePrice: 200000 }
  ];

  const complexityLevels = [
    { id: 'basic', name: 'Basic', multiplier: 1 },
    { id: 'intermediate', name: 'Intermediate', multiplier: 1.5 },
    { id: 'advanced', name: 'Advanced', multiplier: 2.2 },
    { id: 'enterprise', name: 'Enterprise', multiplier: 3 }
  ];

  const additionalFeatures = [
    { id: 'auth', name: 'User Authentication', price: 15000 },
    { id: 'payment', name: 'Payment Integration', price: 25000 },
    { id: 'admin', name: 'Admin Dashboard', price: 35000 },
    { id: 'api', name: 'API Development', price: 30000 },
    { id: 'analytics', name: 'Analytics Integration', price: 20000 },
    { id: 'chat', name: 'Real-time Chat', price: 40000 },
    { id: 'notifications', name: 'Push Notifications', price: 18000 },
    { id: 'cms', name: 'Content Management', price: 28000 }
  ];

  const timelineOptions = [
    { id: 'rush', name: '1-2 months (Rush)', multiplier: 1.5 },
    { id: 'standard', name: '3-4 months (Standard)', multiplier: 1 },
    { id: 'extended', name: '5-6 months (Extended)', multiplier: 0.9 }
  ];

  const calculatePrice = () => {
    const selectedProject = projectTypes.find(p => p.id === projectType);
    const selectedComplexity = complexityLevels.find(c => c.id === complexity);
    const selectedTimeline = timelineOptions.find(t => t.id === timeline);

    if (!selectedProject || !selectedComplexity || !selectedTimeline) return 0;

    let basePrice = selectedProject.basePrice;
    basePrice *= selectedComplexity.multiplier;
    
    const featuresPrice = features.reduce((total, featureId) => {
      const feature = additionalFeatures.find(f => f.id === featureId);
      return total + (feature?.price || 0);
    }, 0);

    const totalPrice = (basePrice + featuresPrice) * selectedTimeline.multiplier;
    return Math.round(totalPrice);
  };

  const handleFeatureToggle = (featureId: string) => {
    setFeatures(prev => 
      prev.includes(featureId) 
        ? prev.filter(id => id !== featureId)
        : [...prev, featureId]
    );
  };

  const handleGetQuote = () => {
    const selectedProject = projectTypes.find(p => p.id === projectType);
    const selectedComplexity = complexityLevels.find(c => c.id === complexity);
    const selectedTimeline = timelineOptions.find(t => t.id === timeline);
    const selectedFeatures = features.map(id => 
      additionalFeatures.find(f => f.id === id)?.name
    ).filter(Boolean);

    const message = `Hi! I used your pricing calculator and I'm interested in:

Project Type: ${selectedProject?.name}
Complexity: ${selectedComplexity?.name}
Timeline: ${selectedTimeline?.name}
Features: ${selectedFeatures.join(', ') || 'None selected'}

Estimated Price: R${calculatePrice().toLocaleString()}

I'd like to discuss this project further.`;

    const whatsappUrl = `https://wa.me/27794484159?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const estimatedPrice = calculatePrice();

  return (
    <section id="pricing-calculator" className="py-20 relative">
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md border border-yellow-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-yellow-500/20">
              <CalculatorIcon className="w-8 h-8 text-yellow-400" />
              <span className="text-yellow-400 font-semibold text-2xl">Pricing Calculator</span>
            </div>
            <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
              <span className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 bg-clip-text text-transparent drop-shadow-2xl">
                GET INSTANT ESTIMATE
              </span>
            </h2>
            <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
              Calculate your <span className="text-yellow-400 font-semibold">project cost</span> in real-time with our
              <span className="text-orange-400 font-semibold"> intelligent pricing calculator</span>.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Calculator Form */}
            <div className="lg:col-span-2 space-y-8">
              {/* Project Type */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-8 shadow-2xl"
              >
                <h3 className="text-3xl font-bold text-white mb-6">Project Type</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {projectTypes.map((type) => (
                    <button
                      key={type.id}
                      onClick={() => setProjectType(type.id)}
                      className={`p-4 rounded-2xl border-2 transition-all duration-300 text-left ${
                        projectType === type.id
                          ? 'border-yellow-400 bg-yellow-400/20 text-white'
                          : 'border-white/30 bg-white/5 text-gray-300 hover:border-yellow-400/50'
                      }`}
                    >
                      <div className="font-bold text-lg">{type.name}</div>
                      <div className="text-sm opacity-80">From R{type.basePrice.toLocaleString()}</div>
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Complexity */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-8 shadow-2xl"
              >
                <h3 className="text-3xl font-bold text-white mb-6">Complexity Level</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {complexityLevels.map((level) => (
                    <button
                      key={level.id}
                      onClick={() => setComplexity(level.id)}
                      className={`p-4 rounded-2xl border-2 transition-all duration-300 text-left ${
                        complexity === level.id
                          ? 'border-orange-400 bg-orange-400/20 text-white'
                          : 'border-white/30 bg-white/5 text-gray-300 hover:border-orange-400/50'
                      }`}
                    >
                      <div className="font-bold text-lg">{level.name}</div>
                      <div className="text-sm opacity-80">{level.multiplier}x multiplier</div>
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Additional Features */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-8 shadow-2xl"
              >
                <h3 className="text-3xl font-bold text-white mb-6">Additional Features</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {additionalFeatures.map((feature) => (
                    <button
                      key={feature.id}
                      onClick={() => handleFeatureToggle(feature.id)}
                      className={`p-4 rounded-2xl border-2 transition-all duration-300 text-left flex items-center justify-between ${
                        features.includes(feature.id)
                          ? 'border-green-400 bg-green-400/20 text-white'
                          : 'border-white/30 bg-white/5 text-gray-300 hover:border-green-400/50'
                      }`}
                    >
                      <div>
                        <div className="font-bold text-lg">{feature.name}</div>
                        <div className="text-sm opacity-80">+R{feature.price.toLocaleString()}</div>
                      </div>
                      {features.includes(feature.id) && (
                        <CheckIcon className="w-6 h-6 text-green-400" />
                      )}
                    </button>
                  ))}
                </div>
              </motion.div>

              {/* Timeline */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-8 shadow-2xl"
              >
                <h3 className="text-3xl font-bold text-white mb-6">Timeline</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {timelineOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => setTimeline(option.id)}
                      className={`p-4 rounded-2xl border-2 transition-all duration-300 text-center ${
                        timeline === option.id
                          ? 'border-purple-400 bg-purple-400/20 text-white'
                          : 'border-white/30 bg-white/5 text-gray-300 hover:border-purple-400/50'
                      }`}
                    >
                      <div className="font-bold text-lg">{option.name}</div>
                      <div className="text-sm opacity-80">{option.multiplier}x multiplier</div>
                    </button>
                  ))}
                </div>
              </motion.div>
            </div>

            {/* Price Display */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-md border-2 border-white/40 rounded-3xl p-8 shadow-2xl sticky top-8"
            >
              <div className="text-center">
                <CurrencyDollarIcon className="w-16 h-16 text-yellow-400 mx-auto mb-6" />
                <h3 className="text-3xl font-bold text-white mb-4">Estimated Price</h3>
                
                {estimatedPrice > 0 ? (
                  <>
                    <div className="text-6xl font-black text-yellow-400 mb-6">
                      R{estimatedPrice.toLocaleString()}
                    </div>
                    <p className="text-gray-300 mb-8">
                      This is an estimated price based on your selections. Final pricing may vary based on specific requirements.
                    </p>
                    <button
                      onClick={handleGetQuote}
                      className="w-full bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-400 hover:to-orange-500 text-white font-bold text-xl px-8 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-yellow-500/50"
                    >
                      GET DETAILED QUOTE
                    </button>
                  </>
                ) : (
                  <div className="text-gray-400 text-xl">
                    Select options above to see estimated pricing
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingCalculator;
