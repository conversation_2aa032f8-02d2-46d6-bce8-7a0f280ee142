"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AdvancedAnimations */ \"(app-pages-browser)/./components/AdvancedAnimations.tsx\");\n/* harmony import */ var _MovingGraphics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MovingGraphics */ \"(app-pages-browser)/./components/MovingGraphics.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar ModernSinglePage = function() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isMenuOpen = _useState[0], setIsMenuOpen = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isScrolled = _useState1[0], setIsScrolled = _useState1[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernSinglePage.useEffect\": function() {\n            var handleScroll = {\n                \"ModernSinglePage.useEffect.handleScroll\": function() {\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ModernSinglePage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernSinglePage.useEffect\": function() {\n                    return window.removeEventListener('scroll', handleScroll);\n                }\n            })[\"ModernSinglePage.useEffect\"];\n        }\n    }[\"ModernSinglePage.useEffect\"], []);\n    var services = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Custom Web Development',\n            description: 'Modern, responsive websites and web applications built with cutting-edge technologies for optimal performance and user experience.',\n            features: [\n                'React/Next.js',\n                'Node.js',\n                'TypeScript',\n                'Progressive Web Apps',\n                'E-commerce Solutions',\n                'CMS Development',\n                'API Integration',\n                'Database Design'\n            ],\n            image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R15,000',\n            timeline: '2-8 weeks',\n            category: 'Web Development',\n            technologies: [\n                'React',\n                'Next.js',\n                'TypeScript',\n                'Tailwind CSS'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: 'Mobile App Development',\n            description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences and modern UI/UX design.',\n            features: [\n                'React Native',\n                'Flutter',\n                'iOS/Android Native',\n                'App Store Optimization',\n                'Push Notifications',\n                'Offline Functionality',\n                'Biometric Auth',\n                'Real-time Sync'\n            ],\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R25,000',\n            timeline: '4-12 weeks',\n            category: 'Mobile Development',\n            technologies: [\n                'React Native',\n                'Flutter',\n                'Swift',\n                'Kotlin'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Cloud & DevOps Solutions',\n            description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions for enterprise-grade applications with 99.9% uptime.',\n            features: [\n                'AWS/Azure/GCP',\n                'Docker/Kubernetes',\n                'CI/CD Pipelines',\n                'Monitoring & Analytics',\n                'Auto-scaling',\n                'Security',\n                'Load Balancing',\n                'Disaster Recovery'\n            ],\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R20,000',\n            timeline: '3-10 weeks',\n            category: 'Cloud Solutions',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'AI/ML Solutions',\n            description: 'Intelligent systems powered by artificial intelligence and machine learning for data-driven business insights and automation.',\n            features: [\n                'Natural Language Processing',\n                'Computer Vision',\n                'Predictive Analytics',\n                'Chatbots',\n                'Data Mining',\n                'Deep Learning',\n                'Neural Networks',\n                'Model Training'\n            ],\n            image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R30,000',\n            timeline: '6-16 weeks',\n            category: 'AI/ML',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'PyTorch',\n                'OpenAI'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Enterprise Software',\n            description: 'Custom enterprise solutions including ERP, CRM, and workflow automation systems for large organizations with advanced security.',\n            features: [\n                'ERP Systems',\n                'CRM Solutions',\n                'Workflow Automation',\n                'Data Analytics',\n                'Integration APIs',\n                'Security Compliance',\n                'User Management',\n                'Reporting'\n            ],\n            image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R50,000',\n            timeline: '8-24 weeks',\n            category: 'Enterprise',\n            technologies: [\n                'Java',\n                'Spring',\n                'PostgreSQL',\n                'Redis'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: 'Digital Transformation',\n            description: 'Complete digital transformation services to modernize your business processes and technology infrastructure for the digital age.',\n            features: [\n                'Process Automation',\n                'Legacy System Migration',\n                'Digital Strategy',\n                'Change Management',\n                'Training & Support',\n                'ROI Analysis',\n                'Performance Optimization',\n                'Scalability Planning'\n            ],\n            image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R75,000',\n            timeline: '12-36 weeks',\n            category: 'Transformation',\n            technologies: [\n                'Microservices',\n                'API Gateway',\n                'Event Streaming',\n                'Analytics'\n            ]\n        }\n    ];\n    var stats = [\n        {\n            number: '500+',\n            label: 'Projects Delivered'\n        },\n        {\n            number: '50+',\n            label: 'Happy Clients'\n        },\n        {\n            number: '5+',\n            label: 'Years Experience'\n        },\n        {\n            number: '24/7',\n            label: 'Support'\n        }\n    ];\n    var testimonials = [\n        {\n            name: 'Sarah Johnson',\n            position: 'CEO',\n            company: 'TechStore Inc.',\n            image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            text: 'AMPD Dev-IQ transformed our online presence completely. Their e-commerce platform increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',\n            rating: 5,\n            project: 'E-Commerce Platform'\n        },\n        {\n            name: 'Dr. Michael Chen',\n            position: 'Chief Medical Officer',\n            company: 'HealthCare Plus',\n            image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',\n            text: 'The healthcare management system has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',\n            rating: 5,\n            project: 'Healthcare Management System'\n        },\n        {\n            name: 'Jennifer Williams',\n            position: 'Digital Banking Director',\n            company: 'SecureBank',\n            image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',\n            text: 'Our customers love the new mobile banking app. It\\'s intuitive, secure, and feature-rich. AMPD Dev-IQ delivered exactly what we needed to compete in the digital banking space.',\n            rating: 5,\n            project: 'Mobile Banking App'\n        },\n        {\n            name: 'David Rodriguez',\n            position: 'Operations Manager',\n            company: 'LogiFlow Corp',\n            image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            text: 'The logistics management system streamlined our entire operation. Real-time tracking and automated workflows have reduced our operational costs by 40%.',\n            rating: 5,\n            project: 'Logistics Management System'\n        }\n    ];\n    var portfolio = [\n        {\n            id: 1,\n            title: 'Advanced E-Commerce Platform',\n            category: 'Web Development',\n            description: 'A comprehensive e-commerce platform with AI-powered recommendations, real-time inventory management, and advanced analytics dashboard.',\n            image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Next.js',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Stripe'\n            ],\n            impact: '300% increase in sales, 50% reduction in cart abandonment, 2.5M+ users',\n            client: 'TechStore Inc.',\n            duration: '6 months',\n            features: [\n                'AI Recommendations',\n                'Real-time Inventory',\n                'Multi-payment Gateway',\n                'Admin Dashboard'\n            ]\n        },\n        {\n            id: 2,\n            title: 'Healthcare Management System',\n            category: 'Enterprise Software',\n            description: 'Comprehensive healthcare management system with patient records, appointment scheduling, telemedicine, and HIPAA compliance.',\n            image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Python',\n                'Django',\n                'MongoDB',\n                'Docker',\n                'WebRTC'\n            ],\n            impact: '60% reduction in administrative time, improved patient satisfaction by 85%',\n            client: 'HealthCare Plus',\n            duration: '8 months',\n            features: [\n                'Patient Records',\n                'Telemedicine',\n                'Appointment Scheduling',\n                'Billing System'\n            ]\n        },\n        {\n            id: 3,\n            title: 'Mobile Banking Application',\n            category: 'Mobile Development',\n            description: 'Secure mobile banking application with biometric authentication, real-time transactions, and advanced security features.',\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React Native',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Biometric SDK'\n            ],\n            impact: '2M+ downloads, 4.8 star rating, 95% user retention, 99.9% uptime',\n            client: 'SecureBank',\n            duration: '10 months',\n            features: [\n                'Biometric Auth',\n                'Real-time Transactions',\n                'Investment Tracking',\n                'Budget Planning'\n            ]\n        },\n        {\n            id: 4,\n            title: 'AI-Powered Analytics Dashboard',\n            category: 'AI/ML',\n            description: 'Advanced analytics dashboard with machine learning insights, predictive analytics, and real-time data visualization.',\n            image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'React',\n                'D3.js',\n                'Apache Kafka',\n                'Redis'\n            ],\n            impact: '40% improvement in decision making, 60% faster insights generation',\n            client: 'DataCorp Analytics',\n            duration: '5 months',\n            features: [\n                'Predictive Analytics',\n                'Real-time Dashboards',\n                'ML Models',\n                'Data Visualization'\n            ]\n        },\n        {\n            id: 5,\n            title: 'Enterprise Resource Planning System',\n            category: 'Enterprise Software',\n            description: 'Complete ERP solution with inventory management, HR, accounting, and supply chain optimization for manufacturing companies.',\n            image: 'https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Java',\n                'Spring Boot',\n                'Angular',\n                'PostgreSQL',\n                'Docker',\n                'Kubernetes'\n            ],\n            impact: '35% reduction in operational costs, 50% improvement in efficiency',\n            client: 'Manufacturing Solutions Ltd',\n            duration: '12 months',\n            features: [\n                'Inventory Management',\n                'HR Module',\n                'Financial Reporting',\n                'Supply Chain'\n            ]\n        },\n        {\n            id: 6,\n            title: 'Cloud Infrastructure Migration',\n            category: 'Cloud Solutions',\n            description: 'Complete cloud migration from on-premise to AWS with microservices architecture, auto-scaling, and disaster recovery.',\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform',\n                'Jenkins',\n                'Monitoring'\n            ],\n            impact: '70% reduction in infrastructure costs, 99.9% uptime achieved',\n            client: 'Global Enterprises',\n            duration: '4 months',\n            features: [\n                'Auto-scaling',\n                'Load Balancing',\n                'Disaster Recovery',\n                'Monitoring'\n            ]\n        }\n    ];\n    var handleContactSubmit = function(e) {\n        e.preventDefault();\n        var formData = new FormData(e.target);\n        var message = \"Hi! I'm \".concat(formData.get('name'), \" from \").concat(formData.get('company'), \".\\n\\nEmail: \").concat(formData.get('email'), \"\\nBudget: \").concat(formData.get('budget'), \"\\n\\nProject Details:\\n\").concat(formData.get('message'), \"\\n\\nI'd like to discuss this project with you.\");\n        var whatsappUrl = \"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.FloatingParticles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.MorphingBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.MovingShapes, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.CursorFollower, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.AnimatedGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.FloatingTechIcons, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.ParticleSystem, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 150,\n                color: \"#22d3ee\",\n                className: \"top-20 right-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 100,\n                color: \"#a855f7\",\n                className: \"bottom-40 left-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 80,\n                color: \"#f97316\",\n                className: \"top-1/2 left-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://media.giphy.com/media/qgQUggAC3Pfv687qPC/giphy.gif\",\n                                alt: \"Software Development Animation\",\n                                className: \"w-full h-full object-cover opacity-20\",\n                                style: {\n                                    filter: 'hue-rotate(200deg) saturate(0.8)',\n                                    animation: 'infinite-loop 20s linear infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-15\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif\",\n                            alt: \"Professional Coding Animation\",\n                            className: \"w-full h-full object-cover mix-blend-overlay\",\n                            style: {\n                                filter: 'brightness(0.7) contrast(1.2)',\n                                animation: 'infinite-loop 15s linear infinite reverse'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif\",\n                            alt: \"Data Flow Animation\",\n                            className: \"w-full h-full object-cover mix-blend-screen\",\n                            style: {\n                                filter: 'hue-rotate(120deg) brightness(0.8)',\n                                animation: 'infinite-loop 25s linear infinite'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.header, {\n                        initial: {\n                            y: -100\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-8 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/ampd-logo.png\",\n                                                        alt: \"AMPD Dev-IQ\",\n                                                        className: \"w-12 h-12 object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-black text-white\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-cyan-400\",\n                                                            children: \"Smart Software. Limitless Potential.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#services\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#portfolio\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: function() {\n                                                return setIsMenuOpen(!isMenuOpen);\n                                            },\n                                            className: \"lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, _this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: \"lg:hidden mt-4 py-4 border-t border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#services\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#portfolio\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"min-h-screen flex items-center justify-center px-8 pt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-7 h-7 text-cyan-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg neon-glow\",\n                                            children: \"Trusted by 500+ Companies Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.InteractiveBlob, {\n                                            className: \"absolute inset-0 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"SMART SOFTWARE.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.GlitchText, {\n                                            text: \"LIMITLESS\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"POTENTIAL.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"We transform your \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyan-400 font-semibold\",\n                                            children: \"business ideas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 33\n                                        }, _this),\n                                        \" into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400 font-semibold\",\n                                            children: \" powerful digital solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, _this),\n                                        \" that drive growth and innovation.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"flex flex-col md:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                            onClick: function() {\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            className: \"text-xl px-12 py-6 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Start Your Project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                            href: \"#about\",\n                                            className: \"group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                boxShadow: \"0 0 30px rgba(34, 211, 238, 0.5)\"\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Watch Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: stats.map(function(stat, index) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 font-semibold\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"services\",\n                            className: \"professional-section relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://media.giphy.com/media/LaVp0AyqR5bGsC5Cbm/giphy.gif\",\n                                            alt: \"Professional Team Collaboration\",\n                                            className: \"w-full h-full object-cover\",\n                                            style: {\n                                                filter: 'brightness(0.4) contrast(1.1)',\n                                                animation: 'infinite-loop 30s linear infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/85 to-cyan-900/85\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR SERVICES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8\",\n                                                    children: \"Comprehensive digital solutions to transform your business with cutting-edge technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\",\n                                            children: services.map(function(service, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 mb-6 rounded-2xl overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: service.image,\n                                                                        alt: service.title,\n                                                                        className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-4 left-4 right-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between text-white\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold bg-cyan-500/20 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                                    children: service.price\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 578,\n                                                                                    columnNumber: 27\n                                                                                }, _this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm bg-purple-500/20 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                                    children: service.timeline\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 581,\n                                                                                    columnNumber: 27\n                                                                                }, _this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-2xl shadow-cyan-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 591,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 590,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                            children: service.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 593,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-6 leading-relaxed group-hover:text-gray-200 transition-colors duration-300\",\n                                                                children: service.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-3 mb-6\",\n                                                                children: service.features.slice(0, 4).map(function(feature, featureIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-cyan-400 mr-3 group-hover:text-cyan-300 transition-colors duration-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 608,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 609,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 25\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                className: \"w-full bg-gradient-to-r from-cyan-500/20 to-purple-600/20 hover:from-cyan-500/30 hover:to-purple-600/30 border border-cyan-400/30 text-white font-semibold py-3 rounded-xl transition-all duration-300\",\n                                                                onClick: function() {\n                                                                    var _document_getElementById;\n                                                                    return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                                        behavior: 'smooth'\n                                                                    });\n                                                                },\n                                                                children: \"Get Quote\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"portfolio\",\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Portfolio Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-cyan-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR PORTFOLIO\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Showcasing our successful projects and the impact we've made for our clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                            children: portfolio.map(function(project, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 mb-6 rounded-2xl overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: project.image,\n                                                                        alt: project.title,\n                                                                        className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-4 left-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-semibold bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                            children: project.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 681,\n                                                                            columnNumber: 27\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: project.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                                children: project.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2 mb-6\",\n                                                                children: project.technologies.map(function(tech, techIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-lg\",\n                                                                        children: tech\n                                                                    }, techIndex, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 698,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-white/10 pt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-cyan-400 font-semibold mb-2\",\n                                                                        children: \"Impact:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 709,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: project.impact\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 666,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, project.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 660,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"about\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                        alt: \"Team Working\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-orange-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 729,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"ABOUT US\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 leading-relaxed\",\n                                                    children: [\n                                                        \"At \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyan-400 font-bold ampd-glow\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 745,\n                                                            columnNumber: 22\n                                                        }, _this),\n                                                        \", we believe that smart software has the power to unlock limitless potential in every business. Our mission is to amplify your success through intelligent solutions and innovative technology.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-cyan-400 mb-4 neon-glow\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"Client Satisfaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 751,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 749,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-purple-400 mb-4 neon-glow\",\n                                                                    children: \"100%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 754,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"On-Time Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 755,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 748,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                    title: 'Innovation',\n                                                    desc: 'Cutting-edge solutions'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                    title: 'Quality',\n                                                    desc: 'Premium standards'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    title: 'Reliability',\n                                                    desc: 'Trusted delivery'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                    title: 'Excellence',\n                                                    desc: 'Outstanding results'\n                                                }\n                                            ].map(function(item, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 773,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 760,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 733,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 721,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Testimonials Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-green-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"CLIENT TESTIMONIALS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 807,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Hear what our satisfied clients say about our work and results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 800,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: testimonials.map(function(testimonial, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__._)(Array(testimonial.rating)).map(function(_, i) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-yellow-400 fill-current\"\n                                                                    }, i, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-200 text-lg mb-8 italic leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    testimonial.text,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 836,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: testimonial.image,\n                                                                        alt: testimonial.name,\n                                                                        className: \"w-16 h-16 rounded-full object-cover mr-4 border-2 border-cyan-400/30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 842,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold text-white text-lg\",\n                                                                                children: testimonial.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 848,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-cyan-400 text-sm\",\n                                                                                children: testimonial.position\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: testimonial.company\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 850,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 847,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-6 pt-6 border-t border-white/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Project: \",\n                                                                        testimonial.project\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 815,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 799,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 788,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 787,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Process Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 873,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 872,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"HOW WE WORK\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Our proven process ensures successful project delivery every time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                            children: [\n                                                {\n                                                    step: '01',\n                                                    title: 'Discovery & Planning',\n                                                    description: 'We analyze your requirements, understand your business goals, and create a detailed project roadmap.',\n                                                    icon: '🔍',\n                                                    color: 'from-cyan-500 to-blue-600'\n                                                },\n                                                {\n                                                    step: '02',\n                                                    title: 'Design & Prototype',\n                                                    description: 'Our team creates wireframes, mockups, and interactive prototypes to visualize your solution.',\n                                                    icon: '🎨',\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    step: '03',\n                                                    title: 'Development & Testing',\n                                                    description: 'We build your solution using best practices, with continuous testing and quality assurance.',\n                                                    icon: '⚡',\n                                                    color: 'from-orange-500 to-red-600'\n                                                },\n                                                {\n                                                    step: '04',\n                                                    title: 'Launch & Support',\n                                                    description: 'We deploy your solution and provide ongoing support, maintenance, and optimization.',\n                                                    icon: '🚀',\n                                                    color: 'from-green-500 to-cyan-600'\n                                                }\n                                            ].map(function(process, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card text-center relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-gradient-to-r \".concat(process.color, \" rounded-full flex items-center justify-center mx-auto mb-6 text-white font-black text-2xl shadow-2xl\"),\n                                                                children: process.step\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-4\",\n                                                                children: process.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 946,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: process.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 949,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: process.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 954,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 933,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 929,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 898,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                onClick: function() {\n                                                    var _document_getElementById;\n                                                    return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                        behavior: 'smooth'\n                                                    });\n                                                },\n                                                className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                children: \"Start Your Project Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 881,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 870,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Pricing Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 991,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"PRICING PACKAGES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Transparent pricing for every business size and requirement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                            children: [\n                                                {\n                                                    name: 'Starter',\n                                                    price: 'R15,000',\n                                                    period: 'Starting from',\n                                                    description: 'Perfect for small businesses and startups',\n                                                    features: [\n                                                        'Responsive Website',\n                                                        'Up to 5 Pages',\n                                                        'Contact Forms',\n                                                        'Basic SEO',\n                                                        '3 Months Support',\n                                                        'Mobile Optimized'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-blue-500 to-cyan-600'\n                                                },\n                                                {\n                                                    name: 'Professional',\n                                                    price: 'R35,000',\n                                                    period: 'Starting from',\n                                                    description: 'Ideal for growing businesses',\n                                                    features: [\n                                                        'Custom Web Application',\n                                                        'Database Integration',\n                                                        'User Authentication',\n                                                        'Admin Dashboard',\n                                                        'API Integration',\n                                                        '6 Months Support',\n                                                        'Advanced SEO',\n                                                        'Analytics Setup'\n                                                    ],\n                                                    popular: true,\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    name: 'Enterprise',\n                                                    price: 'R75,000',\n                                                    period: 'Starting from',\n                                                    description: 'For large organizations and complex projects',\n                                                    features: [\n                                                        'Full-Stack Solution',\n                                                        'Cloud Infrastructure',\n                                                        'AI/ML Integration',\n                                                        'Advanced Security',\n                                                        'Scalable Architecture',\n                                                        '12 Months Support',\n                                                        'DevOps Setup',\n                                                        'Training Included'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-orange-500 to-red-600'\n                                                }\n                                            ].map(function(plan, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden \".concat(plan.popular ? 'border-2 border-cyan-400/50 scale-105' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8 text-center\",\n                                                        children: [\n                                                            plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold\",\n                                                                    children: \"Most Popular\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 27\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1077,\n                                                                columnNumber: 25\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-3xl font-bold text-white mb-4\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1084,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-5xl font-black bg-gradient-to-r \".concat(plan.color, \" bg-clip-text text-transparent mb-2\"),\n                                                                        children: plan.price\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1087,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: plan.period\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1090,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-8\",\n                                                                children: plan.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-3 mb-8 text-left\",\n                                                                children: plan.features.map(function(feature, featureIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center text-gray-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-cyan-400 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1098,\n                                                                                columnNumber: 29\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1099,\n                                                                                columnNumber: 29\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1097,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                                onClick: function() {\n                                                                    var message = \"Hi! I'm interested in the \".concat(plan.name, \" package (\").concat(plan.price, \"). Can you provide more details?\");\n                                                                    window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                                },\n                                                                className: \"w-full text-lg py-4\",\n                                                                children: \"Get Started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1104,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1065,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-lg mb-6\",\n                                                    children: \"Need a custom solution? We offer flexible pricing for unique requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1125,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                    onClick: function() {\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                    children: \"Request Custom Quote\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 982,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"contact\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80\",\n                                        alt: \"Contact Background\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1142,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow\",\n                                                children: \"GET IN TOUCH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                children: \"Ready to transform your business? Let's discuss your project and unlock your potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1162,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        title: 'Email Us',\n                                                        details: '<EMAIL>',\n                                                        subtitle: 'Get a detailed response within 24 hours',\n                                                        action: function() {\n                                                            return window.open('mailto:<EMAIL>', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                        title: 'WhatsApp Us',\n                                                        details: '+27 79 448 4159',\n                                                        subtitle: 'Instant messaging for quick queries',\n                                                        action: function() {\n                                                            return window.open('https://wa.me/27794484159', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                        title: 'Book a Call',\n                                                        details: 'Schedule a free consultation',\n                                                        subtitle: '30-minute strategy session',\n                                                        action: function() {\n                                                            return window.open('https://calendly.com/ampd-deviq', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                                        title: 'Based In',\n                                                        details: 'Cape Town, South Africa',\n                                                        subtitle: 'Serving clients globally'\n                                                    }\n                                                ].map(function(contact, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: contact.action,\n                                                        className: \"enhanced-card p-8 flex items-center space-x-6 \".concat(contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1211,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1210,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-white text-xl mb-2\",\n                                                                        children: contact.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1214,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 text-lg mb-1\",\n                                                                        children: contact.details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1215,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    contact.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: contact.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1213,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1205,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"enhanced-card p-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleContactSubmit,\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    placeholder: \"Your Name\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1234,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    placeholder: \"Your Email\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1241,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1233,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"company\",\n                                                            placeholder: \"Company Name\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1249,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            title: \"Budget Range\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Select Budget Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Under R50,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Under R50,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1261,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R50,000 - R150,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R50,000 - R150,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R150,000 - R300,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R150,000 - R300,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R300,000 - R500,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R300,000 - R500,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R500,000+\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R500,000+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1265,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1255,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 6,\n                                                            placeholder: \"Tell us about your project...\",\n                                                            required: true,\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50\",\n                                                            children: \"Send Message via WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1274,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1232,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1225,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1140,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-16 px-8 border-t border-white/10 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                    alt: \"Footer Background\",\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1289,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1288,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"/ampd-logo.png\",\n                                                                    alt: \"AMPD Dev-IQ\",\n                                                                    className: \"w-12 h-12 object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1302,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1301,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-white\",\n                                                                        children: \"AMPD Dev-IQ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1305,\n                                                                        columnNumber: 21\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Smart Software. Limitless Potential.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 21\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1304,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1300,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                        children: \"We are a Cape Town-based software development company specializing in custom web applications, mobile apps, cloud solutions, and AI/ML implementations. Transform your business with our cutting-edge technology solutions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"https://wa.me/27794484159\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1315,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-blue-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1314,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1299,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1336,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Web Development\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1338,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Mobile Apps\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1339,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1339,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Cloud Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1340,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"AI/ML Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1341,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Enterprise Software\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1342,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1342,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#about\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"About Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#portfolio\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Portfolio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1351,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1351,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#contact\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Contact\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1352,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://wa.me/27794484159\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://calendly.com/ampd-deviq\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Book Call\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1354,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1354,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1349,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1347,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1297,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-white/10 pt-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-2\",\n                                                children: \"\\xa9 2024 Developed by AMPD Dev-IQ. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"Built with ❤️ and smart software for limitless potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1359,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1287,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 2,\n                            type: \"spring\",\n                            stiffness: 260,\n                            damping: 20\n                        },\n                        className: \"fixed bottom-6 right-6 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                            href: \"https://wa.me/27794484159?text=Hi! I'm interested in your software development services.\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white shadow-2xl hover:bg-green-600 transition-colors group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-8 h-8 group-hover:animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1385,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 1377,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1371,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, _this);\n};\n_s(ModernSinglePage, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = ModernSinglePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSinglePage);\nvar _c;\n$RefreshReg$(_c, \"ModernSinglePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ModernSinglePage.tsx\n"));

/***/ })

});