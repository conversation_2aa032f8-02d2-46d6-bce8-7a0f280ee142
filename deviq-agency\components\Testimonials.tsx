'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon } from '@heroicons/react/24/solid';

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'CEO',
      company: 'TechStore Inc.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      content: 'DevIQ transformed our online presence completely. Their team delivered a stunning e-commerce platform that increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',
      rating: 5,
      project: 'E-Commerce Platform'
    },
    {
      id: 2,
      name: 'Dr. <PERSON>',
      position: 'Chief Medical Officer',
      company: 'HealthCare Plus',
      image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',
      content: 'The healthcare management system DevIQ built for us has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',
      rating: 5,
      project: 'Healthcare Management System'
    },
    {
      id: 3,
      name: 'Jennifer Williams',
      position: 'Digital Banking Director',
      company: 'SecureBank',
      image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',
      content: 'Our customers love the new mobile banking app. It\'s intuitive, secure, and feature-rich. DevIQ delivered exactly what we needed to compete in the digital banking space.',
      rating: 5,
      project: 'Mobile Banking App'
    },
    {
      id: 4,
      name: 'Robert Davis',
      position: 'VP of Operations',
      company: 'DataCorp Analytics',
      image: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      content: 'The AI-powered analytics dashboard has transformed our business strategy. The insights we get from the machine learning models have led to $2M in cost savings. Incredible ROI!',
      rating: 5,
      project: 'AI Analytics Dashboard'
    },
    {
      id: 5,
      name: 'Lisa Anderson',
      position: 'Facilities Manager',
      company: 'Smart Buildings Co.',
      image: 'https://images.unsplash.com/photo-*************-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      content: 'The IoT platform DevIQ developed has exceeded our expectations. We\'ve seen a 35% reduction in energy costs and 90% improvement in system uptime. Amazing results!',
      rating: 5,
      project: 'Smart IoT Platform'
    },
    {
      id: 6,
      name: 'Prof. David Wilson',
      position: 'Academic Director',
      company: 'EduTech University',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      content: 'Students love the learning platform. Engagement has never been higher, and we\'ve achieved a 95% course completion rate. DevIQ truly understands educational technology.',
      rating: 5,
      project: 'Educational Platform'
    }
  ];

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000);
    return () => clearInterval(interval);
  }, []);

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-yellow-500/20 to-green-500/20 backdrop-blur-md border border-yellow-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-yellow-500/20">
            <span className="text-yellow-400 font-semibold text-2xl">Client Testimonials</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-yellow-400 via-green-500 to-blue-600 bg-clip-text text-transparent drop-shadow-2xl">
              WHAT OUR CLIENTS SAY
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            Don't just take our word for it. Here's what our <span className="text-yellow-400 font-semibold">clients have to say</span> about
            <span className="text-green-400 font-semibold"> working with DevIQ</span>.
          </p>
        </motion.div>

        {/* Enhanced Main Testimonial */}
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-6xl mx-auto"
        >
          <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-12 md:p-16 shadow-2xl relative">
            {/* Enhanced Quote Icon */}
            <div className="absolute top-8 left-8 text-8xl text-yellow-400/30 font-serif">"</div>

            {/* Enhanced Rating */}
            <div className="flex justify-center mb-10">
              {[...Array(currentTestimonial.rating)].map((_, i) => (
                <StarIcon key={i} className="w-10 h-10 text-yellow-400 mx-1 drop-shadow-lg" />
              ))}
            </div>

            {/* Enhanced Testimonial Content */}
            <blockquote className="text-3xl md:text-4xl text-white text-center mb-12 leading-relaxed font-medium">
              {currentTestimonial.content}
            </blockquote>

            {/* Enhanced Client Info */}
            <div className="flex flex-col md:flex-row items-center justify-center space-y-6 md:space-y-0 md:space-x-8">
              <img
                src={currentTestimonial.image}
                alt={currentTestimonial.name}
                className="w-24 h-24 rounded-full object-cover border-4 border-yellow-400/30 shadow-2xl"
              />
              <div className="text-center md:text-left">
                <h4 className="text-3xl font-bold font-poppins text-white mb-2 neon-glow">
                  {currentTestimonial.name.toUpperCase()}
                </h4>
                <p className="text-xl text-gray-200 mb-2">
                  {currentTestimonial.position} at {currentTestimonial.company}
                </p>
                <p className="text-lg text-yellow-400 font-bold">
                  Project: {currentTestimonial.project}
                </p>
              </div>
            </div>

            {/* Enhanced Navigation Arrows */}
            <button
              type="button"
              onClick={prevTestimonial}
              className="absolute left-6 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-gradient-to-r from-yellow-500 to-green-600 rounded-full shadow-2xl flex items-center justify-center hover:from-yellow-400 hover:to-green-500 transition-all duration-300 transform hover:scale-110"
              aria-label="Previous testimonial"
              title="Previous testimonial"
            >
              <ChevronLeftIcon className="w-8 h-8 text-white" />
            </button>
            <button
              type="button"
              onClick={nextTestimonial}
              className="absolute right-6 top-1/2 transform -translate-y-1/2 w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-2xl flex items-center justify-center hover:from-green-400 hover:to-blue-500 transition-all duration-300 transform hover:scale-110"
              aria-label="Next testimonial"
              title="Next testimonial"
            >
              <ChevronRightIcon className="w-8 h-8 text-white" />
            </button>
          </div>
        </motion.div>

        {/* Enhanced Testimonial Indicators */}
        <div className="flex justify-center mt-12 space-x-4">
          {testimonials.map((_, index) => (
            <button
              key={index}
              type="button"
              onClick={() => setCurrentIndex(index)}
              className={`w-4 h-4 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-yellow-400 w-12 shadow-lg shadow-yellow-400/50'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
              title={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>

        {/* Enhanced Client Logos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <p className="text-center text-gray-200 mb-12 font-bold text-2xl">
            TRUSTED BY LEADING COMPANIES WORLDWIDE
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="text-center">
                <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/20 rounded-2xl p-6 h-20 flex items-center justify-center hover:bg-gradient-to-br hover:from-white/20 hover:to-white/10 transition-all duration-300">
                  <span className="text-white font-bold text-lg">
                    {testimonial.company}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-24 grid grid-cols-1 md:grid-cols-4 gap-8"
        >
          <div className="text-center bg-gradient-to-br from-yellow-500/10 to-green-500/10 backdrop-blur-md p-8 rounded-3xl border border-yellow-400/20 shadow-xl">
            <div className="text-8xl font-black bg-gradient-to-r from-yellow-400 to-green-500 bg-clip-text text-transparent mb-4 drop-shadow-lg neon-glow">98%</div>
            <div className="text-gray-200 font-bold text-xl">CLIENT SATISFACTION</div>
          </div>
          <div className="text-center bg-gradient-to-br from-green-500/10 to-blue-500/10 backdrop-blur-md p-8 rounded-3xl border border-green-400/20 shadow-xl">
            <div className="text-8xl font-black bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent mb-4 drop-shadow-lg neon-glow">500+</div>
            <div className="text-gray-200 font-bold text-xl">PROJECTS COMPLETED</div>
          </div>
          <div className="text-center bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-md p-8 rounded-3xl border border-blue-400/20 shadow-xl">
            <div className="text-8xl font-black bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-4 drop-shadow-lg neon-glow">50+</div>
            <div className="text-gray-200 font-bold text-xl">HAPPY CLIENTS</div>
          </div>
          <div className="text-center bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-md p-8 rounded-3xl border border-purple-400/20 shadow-xl">
            <div className="text-8xl font-black bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent mb-4 drop-shadow-lg neon-glow">5+</div>
            <div className="text-gray-200 font-bold text-xl">YEARS EXPERIENCE</div>
          </div>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
