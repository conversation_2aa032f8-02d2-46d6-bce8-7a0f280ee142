"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AdvancedAnimations */ \"(app-pages-browser)/./components/AdvancedAnimations.tsx\");\n/* harmony import */ var _MovingGraphics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MovingGraphics */ \"(app-pages-browser)/./components/MovingGraphics.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar ModernSinglePage = function() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isMenuOpen = _useState[0], setIsMenuOpen = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isScrolled = _useState1[0], setIsScrolled = _useState1[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernSinglePage.useEffect\": function() {\n            var handleScroll = {\n                \"ModernSinglePage.useEffect.handleScroll\": function() {\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ModernSinglePage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernSinglePage.useEffect\": function() {\n                    return window.removeEventListener('scroll', handleScroll);\n                }\n            })[\"ModernSinglePage.useEffect\"];\n        }\n    }[\"ModernSinglePage.useEffect\"], []);\n    var services = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Custom Web Development',\n            description: 'Modern, responsive websites and web applications built with cutting-edge technologies for optimal performance and user experience.',\n            features: [\n                'React/Next.js',\n                'Node.js',\n                'TypeScript',\n                'Progressive Web Apps',\n                'E-commerce Solutions',\n                'CMS Development',\n                'API Integration',\n                'Database Design'\n            ],\n            image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R15,000',\n            timeline: '2-8 weeks',\n            category: 'Web Development',\n            technologies: [\n                'React',\n                'Next.js',\n                'TypeScript',\n                'Tailwind CSS'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: 'Mobile App Development',\n            description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences and modern UI/UX design.',\n            features: [\n                'React Native',\n                'Flutter',\n                'iOS/Android Native',\n                'App Store Optimization',\n                'Push Notifications',\n                'Offline Functionality',\n                'Biometric Auth',\n                'Real-time Sync'\n            ],\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R25,000',\n            timeline: '4-12 weeks',\n            category: 'Mobile Development',\n            technologies: [\n                'React Native',\n                'Flutter',\n                'Swift',\n                'Kotlin'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Cloud & DevOps Solutions',\n            description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions for enterprise-grade applications with 99.9% uptime.',\n            features: [\n                'AWS/Azure/GCP',\n                'Docker/Kubernetes',\n                'CI/CD Pipelines',\n                'Monitoring & Analytics',\n                'Auto-scaling',\n                'Security',\n                'Load Balancing',\n                'Disaster Recovery'\n            ],\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R20,000',\n            timeline: '3-10 weeks',\n            category: 'Cloud Solutions',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'AI/ML Solutions',\n            description: 'Intelligent systems powered by artificial intelligence and machine learning for data-driven business insights and automation.',\n            features: [\n                'Natural Language Processing',\n                'Computer Vision',\n                'Predictive Analytics',\n                'Chatbots',\n                'Data Mining',\n                'Deep Learning',\n                'Neural Networks',\n                'Model Training'\n            ],\n            image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R30,000',\n            timeline: '6-16 weeks',\n            category: 'AI/ML',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'PyTorch',\n                'OpenAI'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Enterprise Software',\n            description: 'Custom enterprise solutions including ERP, CRM, and workflow automation systems for large organizations with advanced security.',\n            features: [\n                'ERP Systems',\n                'CRM Solutions',\n                'Workflow Automation',\n                'Data Analytics',\n                'Integration APIs',\n                'Security Compliance',\n                'User Management',\n                'Reporting'\n            ],\n            image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R50,000',\n            timeline: '8-24 weeks',\n            category: 'Enterprise',\n            technologies: [\n                'Java',\n                'Spring',\n                'PostgreSQL',\n                'Redis'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: 'Digital Transformation',\n            description: 'Complete digital transformation services to modernize your business processes and technology infrastructure for the digital age.',\n            features: [\n                'Process Automation',\n                'Legacy System Migration',\n                'Digital Strategy',\n                'Change Management',\n                'Training & Support',\n                'ROI Analysis',\n                'Performance Optimization',\n                'Scalability Planning'\n            ],\n            image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R75,000',\n            timeline: '12-36 weeks',\n            category: 'Transformation',\n            technologies: [\n                'Microservices',\n                'API Gateway',\n                'Event Streaming',\n                'Analytics'\n            ]\n        }\n    ];\n    var stats = [\n        {\n            number: '500+',\n            label: 'Projects Delivered'\n        },\n        {\n            number: '50+',\n            label: 'Happy Clients'\n        },\n        {\n            number: '5+',\n            label: 'Years Experience'\n        },\n        {\n            number: '24/7',\n            label: 'Support'\n        }\n    ];\n    var testimonials = [\n        {\n            name: 'Sarah Johnson',\n            position: 'CEO',\n            company: 'TechStore Inc.',\n            image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            text: 'AMPD Dev-IQ transformed our online presence completely. Their e-commerce platform increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',\n            rating: 5,\n            project: 'E-Commerce Platform'\n        },\n        {\n            name: 'Dr. Michael Chen',\n            position: 'Chief Medical Officer',\n            company: 'HealthCare Plus',\n            image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',\n            text: 'The healthcare management system has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',\n            rating: 5,\n            project: 'Healthcare Management System'\n        },\n        {\n            name: 'Jennifer Williams',\n            position: 'Digital Banking Director',\n            company: 'SecureBank',\n            image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',\n            text: 'Our customers love the new mobile banking app. It\\'s intuitive, secure, and feature-rich. AMPD Dev-IQ delivered exactly what we needed to compete in the digital banking space.',\n            rating: 5,\n            project: 'Mobile Banking App'\n        },\n        {\n            name: 'David Rodriguez',\n            position: 'Operations Manager',\n            company: 'LogiFlow Corp',\n            image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            text: 'The logistics management system streamlined our entire operation. Real-time tracking and automated workflows have reduced our operational costs by 40%.',\n            rating: 5,\n            project: 'Logistics Management System'\n        }\n    ];\n    var portfolio = [\n        {\n            id: 1,\n            title: 'Advanced E-Commerce Platform',\n            category: 'Web Development',\n            description: 'A comprehensive e-commerce platform with AI-powered recommendations, real-time inventory management, and advanced analytics dashboard.',\n            image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Next.js',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Stripe'\n            ],\n            impact: '300% increase in sales, 50% reduction in cart abandonment, 2.5M+ users',\n            client: 'TechStore Inc.',\n            duration: '6 months',\n            features: [\n                'AI Recommendations',\n                'Real-time Inventory',\n                'Multi-payment Gateway',\n                'Admin Dashboard'\n            ]\n        },\n        {\n            id: 2,\n            title: 'Healthcare Management System',\n            category: 'Enterprise Software',\n            description: 'Comprehensive healthcare management system with patient records, appointment scheduling, telemedicine, and HIPAA compliance.',\n            image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Python',\n                'Django',\n                'MongoDB',\n                'Docker',\n                'WebRTC'\n            ],\n            impact: '60% reduction in administrative time, improved patient satisfaction by 85%',\n            client: 'HealthCare Plus',\n            duration: '8 months',\n            features: [\n                'Patient Records',\n                'Telemedicine',\n                'Appointment Scheduling',\n                'Billing System'\n            ]\n        },\n        {\n            id: 3,\n            title: 'Mobile Banking Application',\n            category: 'Mobile Development',\n            description: 'Secure mobile banking application with biometric authentication, real-time transactions, and advanced security features.',\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React Native',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Biometric SDK'\n            ],\n            impact: '2M+ downloads, 4.8 star rating, 95% user retention, 99.9% uptime',\n            client: 'SecureBank',\n            duration: '10 months',\n            features: [\n                'Biometric Auth',\n                'Real-time Transactions',\n                'Investment Tracking',\n                'Budget Planning'\n            ]\n        },\n        {\n            id: 4,\n            title: 'AI-Powered Analytics Dashboard',\n            category: 'AI/ML',\n            description: 'Advanced analytics dashboard with machine learning insights, predictive analytics, and real-time data visualization.',\n            image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'React',\n                'D3.js',\n                'Apache Kafka',\n                'Redis'\n            ],\n            impact: '40% improvement in decision making, 60% faster insights generation',\n            client: 'DataCorp Analytics',\n            duration: '5 months',\n            features: [\n                'Predictive Analytics',\n                'Real-time Dashboards',\n                'ML Models',\n                'Data Visualization'\n            ]\n        },\n        {\n            id: 5,\n            title: 'Enterprise Resource Planning System',\n            category: 'Enterprise Software',\n            description: 'Complete ERP solution with inventory management, HR, accounting, and supply chain optimization for manufacturing companies.',\n            image: 'https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Java',\n                'Spring Boot',\n                'Angular',\n                'PostgreSQL',\n                'Docker',\n                'Kubernetes'\n            ],\n            impact: '35% reduction in operational costs, 50% improvement in efficiency',\n            client: 'Manufacturing Solutions Ltd',\n            duration: '12 months',\n            features: [\n                'Inventory Management',\n                'HR Module',\n                'Financial Reporting',\n                'Supply Chain'\n            ]\n        },\n        {\n            id: 6,\n            title: 'Cloud Infrastructure Migration',\n            category: 'Cloud Solutions',\n            description: 'Complete cloud migration from on-premise to AWS with microservices architecture, auto-scaling, and disaster recovery.',\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform',\n                'Jenkins',\n                'Monitoring'\n            ],\n            impact: '70% reduction in infrastructure costs, 99.9% uptime achieved',\n            client: 'Global Enterprises',\n            duration: '4 months',\n            features: [\n                'Auto-scaling',\n                'Load Balancing',\n                'Disaster Recovery',\n                'Monitoring'\n            ]\n        }\n    ];\n    var handleContactSubmit = function(e) {\n        e.preventDefault();\n        var formData = new FormData(e.target);\n        var message = \"Hi! I'm \".concat(formData.get('name'), \" from \").concat(formData.get('company'), \".\\n\\nEmail: \").concat(formData.get('email'), \"\\nBudget: \").concat(formData.get('budget'), \"\\n\\nProject Details:\\n\").concat(formData.get('message'), \"\\n\\nI'd like to discuss this project with you.\");\n        var whatsappUrl = \"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.FloatingParticles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.MorphingBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.MovingShapes, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.CursorFollower, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.AnimatedGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.FloatingTechIcons, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.ParticleSystem, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 150,\n                color: \"#22d3ee\",\n                className: \"top-20 right-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 100,\n                color: \"#a855f7\",\n                className: \"bottom-40 left-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 80,\n                color: \"#f97316\",\n                className: \"top-1/2 left-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://media.giphy.com/media/qgQUggAC3Pfv687qPC/giphy.gif\",\n                                alt: \"Software Development Animation\",\n                                className: \"w-full h-full object-cover opacity-20\",\n                                style: {\n                                    filter: 'hue-rotate(200deg) saturate(0.8)',\n                                    animation: 'infinite-loop 20s linear infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-15\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif\",\n                            alt: \"Professional Coding Animation\",\n                            className: \"w-full h-full object-cover mix-blend-overlay\",\n                            style: {\n                                filter: 'brightness(0.7) contrast(1.2)',\n                                animation: 'infinite-loop 15s linear infinite reverse'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif\",\n                            alt: \"Data Flow Animation\",\n                            className: \"w-full h-full object-cover mix-blend-screen\",\n                            style: {\n                                filter: 'hue-rotate(120deg) brightness(0.8)',\n                                animation: 'infinite-loop 25s linear infinite'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.header, {\n                        initial: {\n                            y: -100\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-8 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/ampd-logo.png\",\n                                                        alt: \"AMPD Dev-IQ\",\n                                                        className: \"w-12 h-12 object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-black text-white\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-cyan-400\",\n                                                            children: \"Smart Software. Limitless Potential.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#services\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#portfolio\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: function() {\n                                                return setIsMenuOpen(!isMenuOpen);\n                                            },\n                                            className: \"lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, _this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: \"lg:hidden mt-4 py-4 border-t border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#services\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#portfolio\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"min-h-screen flex items-center justify-center px-8 pt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-7 h-7 text-cyan-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg neon-glow\",\n                                            children: \"Trusted by 500+ Companies Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.InteractiveBlob, {\n                                            className: \"absolute inset-0 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"SMART SOFTWARE.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.GlitchText, {\n                                            text: \"LIMITLESS\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"POTENTIAL.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"We transform your \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyan-400 font-semibold\",\n                                            children: \"business ideas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 33\n                                        }, _this),\n                                        \" into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400 font-semibold\",\n                                            children: \" powerful digital solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, _this),\n                                        \" that drive growth and innovation.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"flex flex-col md:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                            onClick: function() {\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            className: \"text-xl px-12 py-6 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Start Your Project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                            href: \"#about\",\n                                            className: \"group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                boxShadow: \"0 0 30px rgba(34, 211, 238, 0.5)\"\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Watch Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: stats.map(function(stat, index) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 font-semibold\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"services\",\n                            className: \"professional-section relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://media.giphy.com/media/LaVp0AyqR5bGsC5Cbm/giphy.gif\",\n                                            alt: \"Professional Team Collaboration\",\n                                            className: \"w-full h-full object-cover\",\n                                            style: {\n                                                filter: 'brightness(0.4) contrast(1.1)',\n                                                animation: 'infinite-loop 30s linear infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/85 to-cyan-900/85\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR SERVICES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8\",\n                                                    children: \"Comprehensive digital solutions to transform your business with cutting-edge technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"professional-grid\",\n                                            children: services.map(function(service, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 30\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.8,\n                                                        delay: index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"professional-card group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"professional-image-overlay h-56 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: service.image,\n                                                                    alt: service.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-4 left-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold bg-cyan-500/90 text-white px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                        children: service.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-4 left-4 right-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-white\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-bold bg-gradient-to-r from-cyan-500/80 to-purple-600/80 px-3 py-2 rounded-lg backdrop-blur-sm\",\n                                                                                children: service.price\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-semibold bg-slate-900/80 px-3 py-2 rounded-lg backdrop-blur-sm\",\n                                                                                children: service.timeline\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 25\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-14 h-14 bg-gradient-to-br from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                        className: \"w-7 h-7 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                            children: service.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 23\n                                                                        }, _this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-1\",\n                                                                            children: service.technologies.slice(0, 3).map(function(tech, techIndex) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md\",\n                                                                                    children: tech\n                                                                                }, techIndex, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 27\n                                                                                }, _this);\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 23\n                                                                        }, _this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 mb-6 leading-relaxed text-sm group-hover:text-gray-200 transition-colors duration-300\",\n                                                            children: service.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-semibold text-cyan-400 mb-3\",\n                                                                    children: \"Key Features:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"grid grid-cols-2 gap-2\",\n                                                                    children: service.features.slice(0, 6).map(function(feature, featureIndex) {\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-cyan-400 mr-2 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 27\n                                                                                }, _this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: feature\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 629,\n                                                                                    columnNumber: 27\n                                                                                }, _this)\n                                                                            ]\n                                                                        }, featureIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 25\n                                                                        }, _this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            className: \"professional-button w-full text-sm font-bold py-3\",\n                                                            onClick: function() {\n                                                                var message = \"Hi! I'm interested in \".concat(service.title, \" (\").concat(service.price, \"). Can you provide more details about this service?\");\n                                                                window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                            },\n                                                            children: \"Get Quote & Consultation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"portfolio\",\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Portfolio Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-cyan-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR PORTFOLIO\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Showcasing our successful projects and the impact we've made for our clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                            children: portfolio.map(function(project, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 mb-6 rounded-2xl overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: project.image,\n                                                                        alt: project.title,\n                                                                        className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 702,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-4 left-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-semibold bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                            children: project.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 27\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 696,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: project.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 710,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                                children: project.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2 mb-6\",\n                                                                children: project.technologies.map(function(tech, techIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-lg\",\n                                                                        children: tech\n                                                                    }, techIndex, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-white/10 pt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-cyan-400 font-semibold mb-2\",\n                                                                        children: \"Impact:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: project.impact\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, project.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"about\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                        alt: \"Team Working\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-orange-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 746,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"ABOUT US\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 764,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 leading-relaxed\",\n                                                    children: [\n                                                        \"At \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyan-400 font-bold ampd-glow\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 22\n                                                        }, _this),\n                                                        \", we believe that smart software has the power to unlock limitless potential in every business. Our mission is to amplify your success through intelligent solutions and innovative technology.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-cyan-400 mb-4 neon-glow\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"Client Satisfaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-purple-400 mb-4 neon-glow\",\n                                                                    children: \"100%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 777,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"On-Time Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 778,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 776,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 771,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                    title: 'Innovation',\n                                                    desc: 'Cutting-edge solutions'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                    title: 'Quality',\n                                                    desc: 'Premium standards'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    title: 'Reliability',\n                                                    desc: 'Trusted delivery'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                    title: 'Excellence',\n                                                    desc: 'Outstanding results'\n                                                }\n                                            ].map(function(item, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 801,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 744,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Testimonials Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-green-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"CLIENT TESTIMONIALS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Hear what our satisfied clients say about our work and results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: testimonials.map(function(testimonial, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__._)(Array(testimonial.rating)).map(function(_, i) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-yellow-400 fill-current\"\n                                                                    }, i, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 854,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-200 text-lg mb-8 italic leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    testimonial.text,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: testimonial.image,\n                                                                        alt: testimonial.name,\n                                                                        className: \"w-16 h-16 rounded-full object-cover mr-4 border-2 border-cyan-400/30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 865,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold text-white text-lg\",\n                                                                                children: testimonial.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 871,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-cyan-400 text-sm\",\n                                                                                children: testimonial.position\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 872,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: testimonial.company\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 873,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-6 pt-6 border-t border-white/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Project: \",\n                                                                        testimonial.project\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 840,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 810,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Process Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 895,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"HOW WE WORK\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Our proven process ensures successful project delivery every time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                            children: [\n                                                {\n                                                    step: '01',\n                                                    title: 'Discovery & Planning',\n                                                    description: 'We analyze your requirements, understand your business goals, and create a detailed project roadmap.',\n                                                    icon: '🔍',\n                                                    color: 'from-cyan-500 to-blue-600'\n                                                },\n                                                {\n                                                    step: '02',\n                                                    title: 'Design & Prototype',\n                                                    description: 'Our team creates wireframes, mockups, and interactive prototypes to visualize your solution.',\n                                                    icon: '🎨',\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    step: '03',\n                                                    title: 'Development & Testing',\n                                                    description: 'We build your solution using best practices, with continuous testing and quality assurance.',\n                                                    icon: '⚡',\n                                                    color: 'from-orange-500 to-red-600'\n                                                },\n                                                {\n                                                    step: '04',\n                                                    title: 'Launch & Support',\n                                                    description: 'We deploy your solution and provide ongoing support, maintenance, and optimization.',\n                                                    icon: '🚀',\n                                                    color: 'from-green-500 to-cyan-600'\n                                                }\n                                            ].map(function(process, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card text-center relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-gradient-to-r \".concat(process.color, \" rounded-full flex items-center justify-center mx-auto mb-6 text-white font-black text-2xl shadow-2xl\"),\n                                                                children: process.step\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-4\",\n                                                                children: process.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 969,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: process.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 972,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: process.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 977,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                onClick: function() {\n                                                    var _document_getElementById;\n                                                    return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                        behavior: 'smooth'\n                                                    });\n                                                },\n                                                className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                children: \"Start Your Project Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 993,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 893,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 892,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Pricing Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1014,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"PRICING PACKAGES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Transparent pricing for every business size and requirement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1018,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                            children: [\n                                                {\n                                                    name: 'Starter',\n                                                    price: 'R15,000',\n                                                    period: 'Starting from',\n                                                    description: 'Perfect for small businesses and startups',\n                                                    features: [\n                                                        'Responsive Website',\n                                                        'Up to 5 Pages',\n                                                        'Contact Forms',\n                                                        'Basic SEO',\n                                                        '3 Months Support',\n                                                        'Mobile Optimized'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-blue-500 to-cyan-600'\n                                                },\n                                                {\n                                                    name: 'Professional',\n                                                    price: 'R35,000',\n                                                    period: 'Starting from',\n                                                    description: 'Ideal for growing businesses',\n                                                    features: [\n                                                        'Custom Web Application',\n                                                        'Database Integration',\n                                                        'User Authentication',\n                                                        'Admin Dashboard',\n                                                        'API Integration',\n                                                        '6 Months Support',\n                                                        'Advanced SEO',\n                                                        'Analytics Setup'\n                                                    ],\n                                                    popular: true,\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    name: 'Enterprise',\n                                                    price: 'R75,000',\n                                                    period: 'Starting from',\n                                                    description: 'For large organizations and complex projects',\n                                                    features: [\n                                                        'Full-Stack Solution',\n                                                        'Cloud Infrastructure',\n                                                        'AI/ML Integration',\n                                                        'Advanced Security',\n                                                        'Scalable Architecture',\n                                                        '12 Months Support',\n                                                        'DevOps Setup',\n                                                        'Training Included'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-orange-500 to-red-600'\n                                                }\n                                            ].map(function(plan, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden \".concat(plan.popular ? 'border-2 border-cyan-400/50 scale-105' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8 text-center\",\n                                                        children: [\n                                                            plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold\",\n                                                                    children: \"Most Popular\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1101,\n                                                                    columnNumber: 27\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 25\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-3xl font-bold text-white mb-4\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1107,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-5xl font-black bg-gradient-to-r \".concat(plan.color, \" bg-clip-text text-transparent mb-2\"),\n                                                                        children: plan.price\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: plan.period\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1113,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1109,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-8\",\n                                                                children: plan.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-3 mb-8 text-left\",\n                                                                children: plan.features.map(function(feature, featureIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center text-gray-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-cyan-400 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1121,\n                                                                                columnNumber: 29\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1122,\n                                                                                columnNumber: 29\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1118,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                                onClick: function() {\n                                                                    var message = \"Hi! I'm interested in the \".concat(plan.name, \" package (\").concat(plan.price, \"). Can you provide more details?\");\n                                                                    window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                                },\n                                                                className: \"w-full text-lg py-4\",\n                                                                children: \"Get Started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-lg mb-6\",\n                                                    children: \"Need a custom solution? We offer flexible pricing for unique requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                    onClick: function() {\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                    children: \"Request Custom Quote\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1141,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1017,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 1006,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"contact\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80\",\n                                        alt: \"Contact Background\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1166,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1165,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow\",\n                                                children: \"GET IN TOUCH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1182,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                children: \"Ready to transform your business? Let's discuss your project and unlock your potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1185,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        title: 'Email Us',\n                                                        details: '<EMAIL>',\n                                                        subtitle: 'Get a detailed response within 24 hours',\n                                                        action: function() {\n                                                            return window.open('mailto:<EMAIL>', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                        title: 'WhatsApp Us',\n                                                        details: '+27 79 448 4159',\n                                                        subtitle: 'Instant messaging for quick queries',\n                                                        action: function() {\n                                                            return window.open('https://wa.me/27794484159', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                        title: 'Book a Call',\n                                                        details: 'Schedule a free consultation',\n                                                        subtitle: '30-minute strategy session',\n                                                        action: function() {\n                                                            return window.open('https://calendly.com/ampd-deviq', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                                        title: 'Based In',\n                                                        details: 'Cape Town, South Africa',\n                                                        subtitle: 'Serving clients globally'\n                                                    }\n                                                ].map(function(contact, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: contact.action,\n                                                        className: \"enhanced-card p-8 flex items-center space-x-6 \".concat(contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1234,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-white text-xl mb-2\",\n                                                                        children: contact.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1237,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 text-lg mb-1\",\n                                                                        children: contact.details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1238,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    contact.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: contact.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1240,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1236,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"enhanced-card p-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleContactSubmit,\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    placeholder: \"Your Name\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1257,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    placeholder: \"Your Email\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"company\",\n                                                            placeholder: \"Company Name\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1272,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            title: \"Budget Range\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Select Budget Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Under R50,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Under R50,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1284,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R50,000 - R150,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R50,000 - R150,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1285,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R150,000 - R300,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R150,000 - R300,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1286,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R300,000 - R500,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R300,000 - R500,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R500,000+\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R500,000+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1288,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1278,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 6,\n                                                            placeholder: \"Tell us about your project...\",\n                                                            required: true,\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1290,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50\",\n                                                            children: \"Send Message via WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1255,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1248,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1190,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1163,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-16 px-8 border-t border-white/10 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                    alt: \"Footer Background\",\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1312,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1311,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"/ampd-logo.png\",\n                                                                    alt: \"AMPD Dev-IQ\",\n                                                                    className: \"w-12 h-12 object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1325,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1324,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-white\",\n                                                                        children: \"AMPD Dev-IQ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1328,\n                                                                        columnNumber: 21\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Smart Software. Limitless Potential.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1329,\n                                                                        columnNumber: 21\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1327,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1323,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                        children: \"We are a Cape Town-based software development company specializing in custom web applications, mobile apps, cloud solutions, and AI/ML implementations. Transform your business with our cutting-edge technology solutions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1332,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"https://wa.me/27794484159\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1345,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-blue-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1352,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1347,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1337,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1322,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Web Development\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1361,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1361,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Mobile Apps\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1362,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Cloud Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1363,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"AI/ML Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1364,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Enterprise Software\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1365,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#about\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"About Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#portfolio\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Portfolio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1374,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#contact\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Contact\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1375,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1375,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://wa.me/27794484159\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://calendly.com/ampd-deviq\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Book Call\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1377,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1372,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1370,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-white/10 pt-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-2\",\n                                                children: \"\\xa9 2024 Developed by AMPD Dev-IQ. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1383,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"Built with ❤️ and smart software for limitless potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1386,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1382,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1319,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1310,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 2,\n                            type: \"spring\",\n                            stiffness: 260,\n                            damping: 20\n                        },\n                        className: \"fixed bottom-6 right-6 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                            href: \"https://wa.me/27794484159?text=Hi! I'm interested in your software development services.\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white shadow-2xl hover:bg-green-600 transition-colors group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-8 h-8 group-hover:animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1408,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 1400,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1394,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, _this);\n};\n_s(ModernSinglePage, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = ModernSinglePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSinglePage);\nvar _c;\n$RefreshReg$(_c, \"ModernSinglePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ModernSinglePage.tsx\n"));

/***/ })

});