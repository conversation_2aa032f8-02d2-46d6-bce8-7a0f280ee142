'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    budget: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Create WhatsApp message
    const whatsappMessage = `Hi! I'm ${formData.name} from ${formData.company || 'my company'}.

Email: ${formData.email}
Budget: ${formData.budget || 'Not specified'}

Project Details:
${formData.message}

I'd like to discuss this project with you.`;

    // Open WhatsApp with the message
    const whatsappUrl = `https://wa.me/27794484159?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');

    // Reset form
    setFormData({
      name: '',
      email: '',
      company: '',
      budget: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: EnvelopeIcon,
      title: 'Email Us',
      details: '<EMAIL>',
      description: 'Send us an email anytime!',
      action: () => window.open('mailto:<EMAIL>', '_blank')
    },
    {
      icon: PhoneIcon,
      title: 'WhatsApp Us',
      details: '+27 79 448 4159',
      description: 'Chat with us on WhatsApp',
      action: () => window.open('https://wa.me/27794484159', '_blank')
    },
    {
      icon: MapPinIcon,
      title: 'Based In',
      details: 'Cape Town, South Africa',
      description: 'Operating digitally worldwide'
    },
    {
      icon: CalendarDaysIcon,
      title: 'Schedule a Call',
      details: 'Book a free consultation',
      description: 'Let\'s discuss your project',
      action: () => window.open('https://wa.me/27794484159?text=Hi! I would like to schedule a consultation.', '_blank')
    }
  ];

  const budgetRanges = [
    'Under R50,000',
    'R50,000 - R150,000',
    'R150,000 - R300,000',
    'R300,000 - R500,000',
    'R500,000+'
  ];

  return (
    <section id="contact" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-green-500/20 to-cyan-500/20 backdrop-blur-md border border-green-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-green-500/20">
            <span className="text-green-400 font-semibold text-2xl">Contact Us</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent drop-shadow-2xl">
              LET'S BUILD SOMETHING AMAZING
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            Ready to <span className="text-green-400 font-semibold">transform your ideas into reality</span>? Get in touch with our team and let's
            <span className="text-cyan-400 font-semibold"> discuss your project</span>.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl p-10 shadow-2xl"
          >
            <h3 className="text-giant font-black font-poppins mb-12 text-white neon-glow">
              SEND US A MESSAGE
            </h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-xl font-bold text-white mb-4">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 transition-all text-white text-lg placeholder-gray-300 backdrop-blur-md"
                    placeholder="John Doe"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-xl font-bold text-white mb-4">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 transition-all text-white text-lg placeholder-gray-300 backdrop-blur-md"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="company" className="block text-xl font-bold text-white mb-4">
                  Company Name
                </label>
                <input
                  type="text"
                  id="company"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 transition-all text-white text-lg placeholder-gray-300 backdrop-blur-md"
                  placeholder="Your Company"
                />
              </div>

              <div>
                <label htmlFor="budget" className="block text-xl font-bold text-white mb-4">
                  Project Budget
                </label>
                <select
                  id="budget"
                  name="budget"
                  value={formData.budget}
                  onChange={handleInputChange}
                  className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 transition-all text-white text-lg backdrop-blur-md"
                >
                  <option value="" className="bg-gray-800">Select budget range</option>
                  {budgetRanges.map((range) => (
                    <option key={range} value={range} className="bg-gray-800">
                      {range}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-xl font-bold text-white mb-4">
                  Project Details *
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 transition-all text-white text-lg placeholder-gray-300 backdrop-blur-md resize-none"
                  placeholder="Tell us about your project, goals, and requirements..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-gradient-to-r from-green-500 to-cyan-600 hover:from-green-400 hover:to-cyan-500 text-white font-bold text-2xl px-8 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-green-500/50 border-2 border-green-400/30"
              >
                SEND MESSAGE
              </button>
            </form>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-giant font-black font-poppins mb-12 text-white neon-glow">
                GET IN TOUCH
              </h3>
              <p className="text-3xl text-gray-200 mb-16 leading-relaxed">
                We'd love to hear from you. Choose the most convenient way to <span className="text-green-400 font-semibold">reach out to our team</span>.
              </p>
            </div>

            {/* Enhanced Contact Methods */}
            <div className="space-y-8">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md p-8 rounded-3xl shadow-2xl hover:shadow-green-500/30 transition-all duration-500 transform hover:-translate-y-2 hover:scale-105 border border-white/30 ${info.action ? 'cursor-pointer' : ''}`}
                  onClick={info.action}
                >
                  <div className="flex items-start space-x-6">
                    <div className={`w-20 h-20 bg-gradient-to-r ${
                      index === 0 ? 'from-green-500 to-cyan-500' :
                      index === 1 ? 'from-blue-500 to-purple-500' :
                      index === 2 ? 'from-purple-500 to-pink-500' :
                      'from-cyan-500 to-blue-500'
                    } rounded-3xl flex items-center justify-center flex-shrink-0 shadow-2xl`}>
                      <info.icon className="w-10 h-10 text-white" />
                    </div>
                    <div>
                      <h4 className="text-3xl font-bold font-poppins text-white mb-3 neon-glow">
                        {info.title.toUpperCase()}
                      </h4>
                      <p className="text-cyan-400 font-bold text-xl mb-3">
                        {info.details}
                      </p>
                      <p className="text-gray-300 text-lg">
                        {info.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Enhanced Calendar Integration */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-green-500 to-cyan-600 p-10 rounded-3xl text-white shadow-2xl border-2 border-green-400/30"
            >
              <h4 className="text-3xl font-bold font-poppins mb-6 neon-glow">
                SCHEDULE A FREE CONSULTATION
              </h4>
              <p className="mb-8 text-xl opacity-90">
                Book a 30-minute call with our team to discuss your project requirements and get expert advice.
              </p>
              <button type="button" className="bg-white text-green-600 font-bold text-xl py-4 px-8 rounded-2xl hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-xl">
                BOOK NOW
              </button>
            </motion.div>

            {/* Enhanced Response Time */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-gradient-to-r from-green-500/20 to-cyan-500/20 border-2 border-green-400/30 p-8 rounded-3xl backdrop-blur-md shadow-2xl"
            >
              <div className="flex items-center space-x-4">
                <div className="w-6 h-6 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                <p className="text-green-300 font-bold text-xl">
                  We typically respond within 2 hours during business hours
                </p>
              </div>
            </motion.div>
          </motion.div>
        </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
