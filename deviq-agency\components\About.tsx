'use client';

import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  LightBulbIcon, 
  RocketLaunchIcon, 
  HeartIcon 
} from '@heroicons/react/24/outline';

const About = () => {
  const values = [
    {
      icon: LightBulbIcon,
      title: 'Innovation',
      description: 'We stay ahead of technology trends to deliver cutting-edge solutions.'
    },
    {
      icon: CheckCircleIcon,
      title: 'Quality',
      description: 'Every line of code is crafted with precision and attention to detail.'
    },
    {
      icon: RocketLaunchIcon,
      title: 'Performance',
      description: 'We build fast, scalable solutions that grow with your business.'
    },
    {
      icon: HeartIcon,
      title: 'Passion',
      description: 'We love what we do and it shows in every project we deliver.'
    }
  ];

  const techStack = [
    { name: 'React', category: 'Frontend', color: 'bg-blue-500' },
    { name: 'Next.js', category: 'Frontend', color: 'bg-black' },
    { name: 'TypeScript', category: 'Frontend', color: 'bg-blue-600' },
    { name: 'Node.js', category: 'Backend', color: 'bg-green-600' },
    { name: 'Python', category: 'Backend', color: 'bg-yellow-500' },
    { name: 'PostgreSQL', category: 'Database', color: 'bg-blue-700' },
    { name: 'MongoDB', category: 'Database', color: 'bg-green-500' },
    { name: 'AWS', category: 'Cloud', color: 'bg-orange-500' },
    { name: 'Docker', category: 'DevOps', color: 'bg-blue-400' },
    { name: 'Kubernetes', category: 'DevOps', color: 'bg-blue-600' },
    { name: 'TensorFlow', category: 'AI/ML', color: 'bg-orange-600' },
    { name: 'PyTorch', category: 'AI/ML', color: 'bg-red-500' }
  ];

  const categories = ['All', 'Frontend', 'Backend', 'Database', 'Cloud', 'DevOps', 'AI/ML'];

  return (
    <section id="about" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
          {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-purple-500/20 to-cyan-500/20 backdrop-blur-md border border-purple-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-purple-500/20">
            <span className="text-purple-400 font-semibold text-2xl">About Us</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-purple-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent drop-shadow-2xl">
              ABOUT AMPD DEV-IQ
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            We are a team of <span className="text-purple-400 font-semibold">passionate developers</span>, designers, and innovators dedicated to creating
            <span className="text-cyan-400 font-semibold"> smart software with limitless potential</span>.
          </p>
        </motion.div>

        {/* Enhanced Full-Width Mission & Values Section */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-24 lg:gap-32 items-start mb-32">
          {/* Enhanced Mission & Story */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-8xl lg:text-9xl font-black font-poppins mb-16 text-white neon-glow leading-none">
              OUR MISSION
            </h3>
            <p className="text-4xl lg:text-5xl text-gray-200 mb-16 leading-relaxed font-light">
              At AMPD Dev-IQ, we believe that <span className="text-purple-400 font-bold">smart software has the power to unlock limitless potential</span> in every business.
              Our mission is to amplify your success through <span className="text-cyan-400 font-bold">intelligent solutions and innovative technology</span>, delivering
              software that not only meets today&apos;s needs but anticipates tomorrow&apos;s opportunities.
            </p>
            <p className="text-4xl lg:text-5xl text-gray-200 mb-20 leading-relaxed font-light">
              Founded by a team of industry veterans, we combine years of experience with fresh perspectives
              to tackle complex problems with elegant solutions. We don&apos;t just write code – <span className="text-pink-400 font-bold">we craft digital experiences</span>.
            </p>

            {/* Enhanced Stats - Larger */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="text-center p-12 lg:p-16 bg-gradient-to-br from-purple-500/15 to-pink-500/15 backdrop-blur-xl rounded-3xl shadow-2xl border-2 border-purple-400/30 hover:border-purple-400/50 transition-all duration-500">
                <div className="text-9xl lg:text-10xl font-black bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent mb-8 drop-shadow-lg neon-glow">98%</div>
                <div className="text-gray-200 font-black text-3xl lg:text-4xl">CLIENT SATISFACTION</div>
              </div>
              <div className="text-center p-12 lg:p-16 bg-gradient-to-br from-cyan-500/15 to-blue-500/15 backdrop-blur-xl rounded-3xl shadow-2xl border-2 border-cyan-400/30 hover:border-cyan-400/50 transition-all duration-500">
                <div className="text-9xl lg:text-10xl font-black bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-8 drop-shadow-lg neon-glow">100%</div>
                <div className="text-gray-200 font-black text-3xl lg:text-4xl">ON-TIME DELIVERY</div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Values - Larger */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          >
            {values.map((value, index) => (
              <div key={index} className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl p-12 lg:p-16 rounded-3xl hover:bg-gradient-to-br hover:from-white/25 hover:to-white/10 transition-all duration-700 transform hover:-translate-y-8 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/40 border-2 border-white/40 hover:border-white/60">
                <div className={`w-32 h-32 lg:w-40 lg:h-40 bg-gradient-to-r ${
                  index === 0 ? 'from-purple-500 to-pink-500' :
                  index === 1 ? 'from-cyan-500 to-blue-500' :
                  index === 2 ? 'from-green-500 to-cyan-500' :
                  'from-pink-500 to-purple-500'
                } rounded-3xl flex items-center justify-center mb-12 shadow-2xl shadow-black/30`}>
                  <value.icon className="w-16 h-16 lg:w-20 lg:h-20 text-white" />
                </div>
                <h4 className="text-5xl lg:text-6xl font-black font-poppins mb-10 text-white neon-glow leading-tight">
                  {value.title.toUpperCase()}
                </h4>
                <p className="text-2xl lg:text-3xl text-gray-200 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Enhanced Tech Stack - Full Width */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-8xl lg:text-9xl font-black font-poppins mb-16 text-white neon-glow leading-none">
            OUR TECHNOLOGY STACK
          </h3>
          <p className="text-4xl lg:text-5xl text-gray-200 mb-20 max-w-8xl mx-auto leading-relaxed font-light">
            We work with <span className="text-cyan-400 font-bold">cutting-edge technologies</span> to deliver robust, scalable, and
            <span className="text-purple-400 font-bold"> future-proof solutions</span>.
          </p>

          {/* Enhanced Tech Categories Filter - Larger */}
          <div className="flex flex-wrap justify-center gap-8 mb-20">
            {categories.map((category) => (
              <button
                key={category}
                type="button"
                className="px-12 py-6 rounded-3xl border-4 border-cyan-400/40 bg-gradient-to-r from-cyan-500/15 to-blue-500/15 text-white hover:border-cyan-400/70 hover:bg-gradient-to-r hover:from-cyan-500/25 hover:to-blue-500/25 transition-all duration-300 font-black text-2xl lg:text-3xl backdrop-blur-xl shadow-2xl transform hover:scale-110"
              >
                {category}
              </button>
            ))}
          </div>

          {/* Enhanced Tech Stack Grid - Larger */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-12">
            {techStack.map((tech, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl p-10 lg:p-12 rounded-3xl shadow-2xl hover:shadow-cyan-500/40 transition-all duration-700 group transform hover:-translate-y-8 hover:scale-110 border-2 border-white/40 hover:border-cyan-400/60"
              >
                <div className={`w-24 h-24 lg:w-28 lg:h-28 ${tech.color} rounded-3xl mx-auto mb-8 flex items-center justify-center group-hover:scale-110 transition-transform duration-500 shadow-2xl`}>
                  <span className="text-white font-black text-3xl lg:text-4xl">
                    {tech.name.charAt(0)}
                  </span>
                </div>
                <h4 className="font-black text-white mb-4 text-center text-2xl lg:text-3xl neon-glow">{tech.name}</h4>
                <p className="text-xl lg:text-2xl text-gray-300 text-center font-semibold">{tech.category}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;
