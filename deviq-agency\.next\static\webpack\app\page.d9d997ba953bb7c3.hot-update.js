"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AdvancedAnimations */ \"(app-pages-browser)/./components/AdvancedAnimations.tsx\");\n/* harmony import */ var _MovingGraphics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MovingGraphics */ \"(app-pages-browser)/./components/MovingGraphics.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarDaysIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CalendarDaysIcon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar ModernSinglePage = function() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isMenuOpen = _useState[0], setIsMenuOpen = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isScrolled = _useState1[0], setIsScrolled = _useState1[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernSinglePage.useEffect\": function() {\n            var handleScroll = {\n                \"ModernSinglePage.useEffect.handleScroll\": function() {\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ModernSinglePage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernSinglePage.useEffect\": function() {\n                    return window.removeEventListener('scroll', handleScroll);\n                }\n            })[\"ModernSinglePage.useEffect\"];\n        }\n    }[\"ModernSinglePage.useEffect\"], []);\n    var services = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Custom Web Development',\n            description: 'Modern, responsive websites and web applications built with cutting-edge technologies for optimal performance and user experience.',\n            features: [\n                'React/Next.js',\n                'Node.js',\n                'TypeScript',\n                'Progressive Web Apps',\n                'E-commerce Solutions',\n                'CMS Development',\n                'API Integration',\n                'Database Design'\n            ],\n            image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R15,000',\n            timeline: '2-8 weeks',\n            category: 'Web Development',\n            technologies: [\n                'React',\n                'Next.js',\n                'TypeScript',\n                'Tailwind CSS'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: 'Mobile App Development',\n            description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences and modern UI/UX design.',\n            features: [\n                'React Native',\n                'Flutter',\n                'iOS/Android Native',\n                'App Store Optimization',\n                'Push Notifications',\n                'Offline Functionality',\n                'Biometric Auth',\n                'Real-time Sync'\n            ],\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R25,000',\n            timeline: '4-12 weeks',\n            category: 'Mobile Development',\n            technologies: [\n                'React Native',\n                'Flutter',\n                'Swift',\n                'Kotlin'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Cloud & DevOps Solutions',\n            description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions for enterprise-grade applications with 99.9% uptime.',\n            features: [\n                'AWS/Azure/GCP',\n                'Docker/Kubernetes',\n                'CI/CD Pipelines',\n                'Monitoring & Analytics',\n                'Auto-scaling',\n                'Security',\n                'Load Balancing',\n                'Disaster Recovery'\n            ],\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R20,000',\n            timeline: '3-10 weeks',\n            category: 'Cloud Solutions',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'AI/ML Solutions',\n            description: 'Intelligent systems powered by artificial intelligence and machine learning for data-driven business insights and automation.',\n            features: [\n                'Natural Language Processing',\n                'Computer Vision',\n                'Predictive Analytics',\n                'Chatbots',\n                'Data Mining',\n                'Deep Learning',\n                'Neural Networks',\n                'Model Training'\n            ],\n            image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R30,000',\n            timeline: '6-16 weeks',\n            category: 'AI/ML',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'PyTorch',\n                'OpenAI'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Enterprise Software',\n            description: 'Custom enterprise solutions including ERP, CRM, and workflow automation systems for large organizations with advanced security.',\n            features: [\n                'ERP Systems',\n                'CRM Solutions',\n                'Workflow Automation',\n                'Data Analytics',\n                'Integration APIs',\n                'Security Compliance',\n                'User Management',\n                'Reporting'\n            ],\n            image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R50,000',\n            timeline: '8-24 weeks',\n            category: 'Enterprise',\n            technologies: [\n                'Java',\n                'Spring',\n                'PostgreSQL',\n                'Redis'\n            ]\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: 'Digital Transformation',\n            description: 'Complete digital transformation services to modernize your business processes and technology infrastructure for the digital age.',\n            features: [\n                'Process Automation',\n                'Legacy System Migration',\n                'Digital Strategy',\n                'Change Management',\n                'Training & Support',\n                'ROI Analysis',\n                'Performance Optimization',\n                'Scalability Planning'\n            ],\n            image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            price: 'From R75,000',\n            timeline: '12-36 weeks',\n            category: 'Transformation',\n            technologies: [\n                'Microservices',\n                'API Gateway',\n                'Event Streaming',\n                'Analytics'\n            ]\n        }\n    ];\n    var stats = [\n        {\n            number: '500+',\n            label: 'Projects Delivered'\n        },\n        {\n            number: '50+',\n            label: 'Happy Clients'\n        },\n        {\n            number: '5+',\n            label: 'Years Experience'\n        },\n        {\n            number: '24/7',\n            label: 'Support'\n        }\n    ];\n    var testimonials = [\n        {\n            name: 'Sarah Johnson',\n            position: 'CEO',\n            company: 'TechStore Inc.',\n            image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            text: 'AMPD Dev-IQ transformed our online presence completely. Their e-commerce platform increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',\n            rating: 5,\n            project: 'E-Commerce Platform'\n        },\n        {\n            name: 'Dr. Michael Chen',\n            position: 'Chief Medical Officer',\n            company: 'HealthCare Plus',\n            image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',\n            text: 'The healthcare management system has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',\n            rating: 5,\n            project: 'Healthcare Management System'\n        },\n        {\n            name: 'Jennifer Williams',\n            position: 'Digital Banking Director',\n            company: 'SecureBank',\n            image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',\n            text: 'Our customers love the new mobile banking app. It\\'s intuitive, secure, and feature-rich. AMPD Dev-IQ delivered exactly what we needed to compete in the digital banking space.',\n            rating: 5,\n            project: 'Mobile Banking App'\n        },\n        {\n            name: 'David Rodriguez',\n            position: 'Operations Manager',\n            company: 'LogiFlow Corp',\n            image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            text: 'The logistics management system streamlined our entire operation. Real-time tracking and automated workflows have reduced our operational costs by 40%.',\n            rating: 5,\n            project: 'Logistics Management System'\n        }\n    ];\n    var portfolio = [\n        {\n            id: 1,\n            title: 'Advanced E-Commerce Platform',\n            category: 'Web Development',\n            description: 'A comprehensive e-commerce platform with AI-powered recommendations, real-time inventory management, and advanced analytics dashboard.',\n            image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Next.js',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Stripe'\n            ],\n            impact: '300% increase in sales, 50% reduction in cart abandonment, 2.5M+ users',\n            client: 'TechStore Inc.',\n            duration: '6 months',\n            features: [\n                'AI Recommendations',\n                'Real-time Inventory',\n                'Multi-payment Gateway',\n                'Admin Dashboard'\n            ]\n        },\n        {\n            id: 2,\n            title: 'Healthcare Management System',\n            category: 'Enterprise Software',\n            description: 'Comprehensive healthcare management system with patient records, appointment scheduling, telemedicine, and HIPAA compliance.',\n            image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React',\n                'Python',\n                'Django',\n                'MongoDB',\n                'Docker',\n                'WebRTC'\n            ],\n            impact: '60% reduction in administrative time, improved patient satisfaction by 85%',\n            client: 'HealthCare Plus',\n            duration: '8 months',\n            features: [\n                'Patient Records',\n                'Telemedicine',\n                'Appointment Scheduling',\n                'Billing System'\n            ]\n        },\n        {\n            id: 3,\n            title: 'Mobile Banking Application',\n            category: 'Mobile Development',\n            description: 'Secure mobile banking application with biometric authentication, real-time transactions, and advanced security features.',\n            image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'React Native',\n                'Node.js',\n                'PostgreSQL',\n                'AWS',\n                'Biometric SDK'\n            ],\n            impact: '2M+ downloads, 4.8 star rating, 95% user retention, 99.9% uptime',\n            client: 'SecureBank',\n            duration: '10 months',\n            features: [\n                'Biometric Auth',\n                'Real-time Transactions',\n                'Investment Tracking',\n                'Budget Planning'\n            ]\n        },\n        {\n            id: 4,\n            title: 'AI-Powered Analytics Dashboard',\n            category: 'AI/ML',\n            description: 'Advanced analytics dashboard with machine learning insights, predictive analytics, and real-time data visualization.',\n            image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Python',\n                'TensorFlow',\n                'React',\n                'D3.js',\n                'Apache Kafka',\n                'Redis'\n            ],\n            impact: '40% improvement in decision making, 60% faster insights generation',\n            client: 'DataCorp Analytics',\n            duration: '5 months',\n            features: [\n                'Predictive Analytics',\n                'Real-time Dashboards',\n                'ML Models',\n                'Data Visualization'\n            ]\n        },\n        {\n            id: 5,\n            title: 'Enterprise Resource Planning System',\n            category: 'Enterprise Software',\n            description: 'Complete ERP solution with inventory management, HR, accounting, and supply chain optimization for manufacturing companies.',\n            image: 'https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'Java',\n                'Spring Boot',\n                'Angular',\n                'PostgreSQL',\n                'Docker',\n                'Kubernetes'\n            ],\n            impact: '35% reduction in operational costs, 50% improvement in efficiency',\n            client: 'Manufacturing Solutions Ltd',\n            duration: '12 months',\n            features: [\n                'Inventory Management',\n                'HR Module',\n                'Financial Reporting',\n                'Supply Chain'\n            ]\n        },\n        {\n            id: 6,\n            title: 'Cloud Infrastructure Migration',\n            category: 'Cloud Solutions',\n            description: 'Complete cloud migration from on-premise to AWS with microservices architecture, auto-scaling, and disaster recovery.',\n            image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',\n            technologies: [\n                'AWS',\n                'Docker',\n                'Kubernetes',\n                'Terraform',\n                'Jenkins',\n                'Monitoring'\n            ],\n            impact: '70% reduction in infrastructure costs, 99.9% uptime achieved',\n            client: 'Global Enterprises',\n            duration: '4 months',\n            features: [\n                'Auto-scaling',\n                'Load Balancing',\n                'Disaster Recovery',\n                'Monitoring'\n            ]\n        }\n    ];\n    var handleContactSubmit = function(e) {\n        e.preventDefault();\n        var formData = new FormData(e.target);\n        var message = \"Hi! I'm \".concat(formData.get('name'), \" from \").concat(formData.get('company'), \".\\n\\nEmail: \").concat(formData.get('email'), \"\\nBudget: \").concat(formData.get('budget'), \"\\n\\nProject Details:\\n\").concat(formData.get('message'), \"\\n\\nI'd like to discuss this project with you.\");\n        var whatsappUrl = \"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.FloatingParticles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.MorphingBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.MovingShapes, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.CursorFollower, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.AnimatedGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.FloatingTechIcons, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 272,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.ParticleSystem, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 150,\n                color: \"#22d3ee\",\n                className: \"top-20 right-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 100,\n                color: \"#a855f7\",\n                className: \"bottom-40 left-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 80,\n                color: \"#f97316\",\n                className: \"top-1/2 left-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://media.giphy.com/media/qgQUggAC3Pfv687qPC/giphy.gif\",\n                                alt: \"Software Development Animation\",\n                                className: \"w-full h-full object-cover opacity-20\",\n                                style: {\n                                    filter: 'hue-rotate(200deg) saturate(0.8)',\n                                    animation: 'infinite-loop 20s linear infinite'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-15\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif\",\n                            alt: \"Professional Coding Animation\",\n                            className: \"w-full h-full object-cover mix-blend-overlay\",\n                            style: {\n                                filter: 'brightness(0.7) contrast(1.2)',\n                                animation: 'infinite-loop 15s linear infinite reverse'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif\",\n                            alt: \"Data Flow Animation\",\n                            className: \"w-full h-full object-cover mix-blend-screen\",\n                            style: {\n                                filter: 'hue-rotate(120deg) brightness(0.8)',\n                                animation: 'infinite-loop 25s linear infinite'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.header, {\n                        initial: {\n                            y: -100\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-8 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/ampd-logo.png\",\n                                                        alt: \"AMPD Dev-IQ\",\n                                                        className: \"w-12 h-12 object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-black text-white\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-cyan-400\",\n                                                            children: \"Smart Software. Limitless Potential.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#services\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#portfolio\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: function() {\n                                                return setIsMenuOpen(!isMenuOpen);\n                                            },\n                                            className: \"lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, _this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: \"lg:hidden mt-4 py-4 border-t border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#services\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#portfolio\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"min-h-screen flex items-center justify-center px-8 pt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-7 h-7 text-cyan-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg neon-glow\",\n                                            children: \"Trusted by 500+ Companies Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.InteractiveBlob, {\n                                            className: \"absolute inset-0 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"SMART SOFTWARE.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.GlitchText, {\n                                            text: \"LIMITLESS\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"POTENTIAL.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"We transform your \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyan-400 font-semibold\",\n                                            children: \"business ideas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 33\n                                        }, _this),\n                                        \" into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400 font-semibold\",\n                                            children: \" powerful digital solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, _this),\n                                        \" that drive growth and innovation.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"flex flex-col md:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                            onClick: function() {\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            className: \"text-xl px-12 py-6 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Start Your Project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                            href: \"#about\",\n                                            className: \"group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                boxShadow: \"0 0 30px rgba(34, 211, 238, 0.5)\"\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Watch Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: stats.map(function(stat, index) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 font-semibold\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"services\",\n                            className: \"professional-section relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://media.giphy.com/media/LaVp0AyqR5bGsC5Cbm/giphy.gif\",\n                                            alt: \"Professional Team Collaboration\",\n                                            className: \"w-full h-full object-cover\",\n                                            style: {\n                                                filter: 'brightness(0.4) contrast(1.1)',\n                                                animation: 'infinite-loop 30s linear infinite'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/85 to-cyan-900/85\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR SERVICES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8\",\n                                                    children: \"Comprehensive digital solutions to transform your business with cutting-edge technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"professional-grid\",\n                                            children: services.map(function(service, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 30\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.8,\n                                                        delay: index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"professional-card group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"professional-image-overlay h-56 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: service.image,\n                                                                    alt: service.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-4 left-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-bold bg-cyan-500/90 text-white px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                        children: service.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-4 left-4 right-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between text-white\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-bold bg-gradient-to-r from-cyan-500/80 to-purple-600/80 px-3 py-2 rounded-lg backdrop-blur-sm\",\n                                                                                children: service.price\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 585,\n                                                                                columnNumber: 25\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-semibold bg-slate-900/80 px-3 py-2 rounded-lg backdrop-blur-sm\",\n                                                                                children: service.timeline\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 588,\n                                                                                columnNumber: 25\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-14 h-14 bg-gradient-to-br from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                        className: \"w-7 h-7 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                            children: service.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 23\n                                                                        }, _this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-1\",\n                                                                            children: service.technologies.slice(0, 3).map(function(tech, techIndex) {\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md\",\n                                                                                    children: tech\n                                                                                }, techIndex, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 27\n                                                                                }, _this);\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 604,\n                                                                            columnNumber: 23\n                                                                        }, _this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 600,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300 mb-6 leading-relaxed text-sm group-hover:text-gray-200 transition-colors duration-300\",\n                                                            children: service.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-semibold text-cyan-400 mb-3\",\n                                                                    children: \"Key Features:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"grid grid-cols-2 gap-2\",\n                                                                    children: service.features.slice(0, 6).map(function(feature, featureIndex) {\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-cyan-400 mr-2 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 27\n                                                                                }, _this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs\",\n                                                                                    children: feature\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 629,\n                                                                                    columnNumber: 27\n                                                                                }, _this)\n                                                                            ]\n                                                                        }, featureIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 627,\n                                                                            columnNumber: 25\n                                                                        }, _this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            className: \"professional-button w-full text-sm font-bold py-3\",\n                                                            onClick: function() {\n                                                                var message = \"Hi! I'm interested in \".concat(service.title, \" (\").concat(service.price, \"). Can you provide more details about this service?\");\n                                                                window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                            },\n                                                            children: \"Get Quote & Consultation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 636,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 17\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"portfolio\",\n                            className: \"professional-section relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://media.giphy.com/media/xT9IgzoKnwFNmISR8I/giphy.gif\",\n                                            alt: \"Professional Development Background\",\n                                            className: \"w-full h-full object-cover\",\n                                            style: {\n                                                filter: 'brightness(0.3) contrast(1.2) hue-rotate(200deg)',\n                                                animation: 'infinite-loop 35s linear infinite reverse'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 659,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-cyan-900/85 to-purple-900/85\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR PORTFOLIO\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8\",\n                                                    children: \"Showcasing our successful projects and the measurable impact we've delivered for our clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"professional-grid\",\n                                            children: portfolio.map(function(project, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: 30\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        y: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.8,\n                                                        delay: index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"professional-card group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"professional-image-overlay h-52 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: project.image,\n                                                                    alt: project.title,\n                                                                    className: \"w-full h-full object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 705,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute top-4 left-4 right-4 flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-bold bg-cyan-500/90 text-white px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                            children: project.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 709,\n                                                                            columnNumber: 25\n                                                                        }, _this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-semibold bg-slate-900/80 text-white px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                            children: project.duration\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute bottom-4 left-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-bold text-white bg-gradient-to-r from-purple-500/80 to-pink-600/80 px-3 py-2 rounded-lg backdrop-blur-sm\",\n                                                                        children: project.client\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                    children: project.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300\",\n                                                                    children: project.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 726,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-semibold text-cyan-400 mb-2\",\n                                                                    children: \"Key Features:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 737,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-1\",\n                                                                    children: project.features.map(function(feature, featureIndex) {\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md\",\n                                                                            children: feature\n                                                                        }, featureIndex, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 27\n                                                                        }, _this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-semibold text-orange-400 mb-2\",\n                                                                    children: \"Technologies:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 752,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: project.technologies.map(function(tech, techIndex) {\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded-md\",\n                                                                            children: tech\n                                                                        }, techIndex, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 27\n                                                                        }, _this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 753,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-t border-white/10 pt-4 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-semibold text-green-400 mb-2\",\n                                                                    children: \"Measurable Impact:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 767,\n                                                                    columnNumber: 23\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-300 leading-relaxed\",\n                                                                    children: project.impact\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 768,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 766,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                                            whileHover: {\n                                                                scale: 1.02\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.98\n                                                            },\n                                                            className: \"professional-button w-full text-sm font-bold py-3\",\n                                                            onClick: function() {\n                                                                var message = \"Hi! I'm interested in a similar project to \".concat(project.title, \". Can we discuss my requirements?\");\n                                                                window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                            },\n                                                            children: \"Discuss Similar Project\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, project.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"about\",\n                        className: \"professional-section relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif\",\n                                        alt: \"Professional Team Working\",\n                                        className: \"w-full h-full object-cover\",\n                                        style: {\n                                            filter: 'brightness(0.4) contrast(1.1) hue-rotate(300deg)',\n                                            animation: 'infinite-loop 40s linear infinite'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 794,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-900/85 to-orange-900/85\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"professional-heading bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow mb-8\",\n                                                    children: \"ABOUT AMPD DEV-IQ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-1 bg-gradient-to-r from-purple-400 to-orange-500 mb-8 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed\",\n                                                    children: [\n                                                        \"At \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyan-400 font-bold\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 22\n                                                        }, _this),\n                                                        \", we are a Cape Town-based software development powerhouse specializing in transforming businesses through intelligent technology solutions.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                                    children: \"With over 5 years of experience and 500+ successful projects, we combine cutting-edge technology with deep industry expertise to deliver solutions that drive real business growth. Our team of expert developers, designers, and strategists work collaboratively to turn your vision into reality.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-white mb-4\",\n                                                            children: \"Why Choose Us:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                'Proven track record with 500+ successful projects',\n                                                                'Expert team with 5+ years of industry experience',\n                                                                'Cutting-edge technology stack and best practices',\n                                                                '24/7 support and maintenance services',\n                                                                'Transparent pricing and project management',\n                                                                'Based in Cape Town, serving clients globally'\n                                                            ].map(function(point, index) {\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center text-gray-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-5 h-5 text-cyan-400 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 844,\n                                                                            columnNumber: 25\n                                                                        }, _this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, _this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 834,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"professional-card text-center p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-4xl lg:text-5xl font-black text-cyan-400 mb-2 neon-glow\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg text-gray-300 font-semibold\",\n                                                                    children: \"Client Satisfaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 855,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: \"Based on 50+ reviews\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 856,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"professional-card text-center p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-4xl lg:text-5xl font-black text-purple-400 mb-2 neon-glow\",\n                                                                    children: \"100%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 859,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg text-gray-300 font-semibold\",\n                                                                    children: \"On-Time Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: \"500+ projects delivered\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 858,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 852,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 808,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                    title: 'Innovation',\n                                                    desc: 'Cutting-edge solutions'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                    title: 'Quality',\n                                                    desc: 'Premium standards'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    title: 'Reliability',\n                                                    desc: 'Trusted delivery'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                    title: 'Excellence',\n                                                    desc: 'Outstanding results'\n                                                }\n                                            ].map(function(item, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 884,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 807,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 806,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Testimonials Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-green-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 896,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"CLIENT TESTIMONIALS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 913,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Hear what our satisfied clients say about our work and results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 916,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 906,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: testimonials.map(function(testimonial, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__._)(Array(testimonial.rating)).map(function(_, i) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-yellow-400 fill-current\"\n                                                                    }, i, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-200 text-lg mb-8 italic leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    testimonial.text,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: testimonial.image,\n                                                                        alt: testimonial.name,\n                                                                        className: \"w-16 h-16 rounded-full object-cover mr-4 border-2 border-cyan-400/30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold text-white text-lg\",\n                                                                                children: testimonial.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 954,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-cyan-400 text-sm\",\n                                                                                children: testimonial.position\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 955,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: testimonial.company\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 953,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 947,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-6 pt-6 border-t border-white/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Project: \",\n                                                                        testimonial.project\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 927,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 893,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Process Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 984,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"HOW WE WORK\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Our proven process ensures successful project delivery every time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 988,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                            children: [\n                                                {\n                                                    step: '01',\n                                                    title: 'Discovery & Planning',\n                                                    description: 'We analyze your requirements, understand your business goals, and create a detailed project roadmap.',\n                                                    icon: '🔍',\n                                                    color: 'from-cyan-500 to-blue-600'\n                                                },\n                                                {\n                                                    step: '02',\n                                                    title: 'Design & Prototype',\n                                                    description: 'Our team creates wireframes, mockups, and interactive prototypes to visualize your solution.',\n                                                    icon: '🎨',\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    step: '03',\n                                                    title: 'Development & Testing',\n                                                    description: 'We build your solution using best practices, with continuous testing and quality assurance.',\n                                                    icon: '⚡',\n                                                    color: 'from-orange-500 to-red-600'\n                                                },\n                                                {\n                                                    step: '04',\n                                                    title: 'Launch & Support',\n                                                    description: 'We deploy your solution and provide ongoing support, maintenance, and optimization.',\n                                                    icon: '🚀',\n                                                    color: 'from-green-500 to-cyan-600'\n                                                }\n                                            ].map(function(process, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card text-center relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-gradient-to-r \".concat(process.color, \" rounded-full flex items-center justify-center mx-auto mb-6 text-white font-black text-2xl shadow-2xl\"),\n                                                                children: process.step\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-4\",\n                                                                children: process.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: process.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1055,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: process.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1035,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                onClick: function() {\n                                                    var _document_getElementById;\n                                                    return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                        behavior: 'smooth'\n                                                    });\n                                                },\n                                                className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                children: \"Start Your Project Today\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 975,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Pricing Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"PRICING PACKAGES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Transparent pricing for every business size and requirement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1111,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                                            children: [\n                                                {\n                                                    name: 'Starter',\n                                                    price: 'R15,000',\n                                                    period: 'Starting from',\n                                                    description: 'Perfect for small businesses and startups',\n                                                    features: [\n                                                        'Responsive Website',\n                                                        'Up to 5 Pages',\n                                                        'Contact Forms',\n                                                        'Basic SEO',\n                                                        '3 Months Support',\n                                                        'Mobile Optimized'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-blue-500 to-cyan-600'\n                                                },\n                                                {\n                                                    name: 'Professional',\n                                                    price: 'R35,000',\n                                                    period: 'Starting from',\n                                                    description: 'Ideal for growing businesses',\n                                                    features: [\n                                                        'Custom Web Application',\n                                                        'Database Integration',\n                                                        'User Authentication',\n                                                        'Admin Dashboard',\n                                                        'API Integration',\n                                                        '6 Months Support',\n                                                        'Advanced SEO',\n                                                        'Analytics Setup'\n                                                    ],\n                                                    popular: true,\n                                                    color: 'from-purple-500 to-pink-600'\n                                                },\n                                                {\n                                                    name: 'Enterprise',\n                                                    price: 'R75,000',\n                                                    period: 'Starting from',\n                                                    description: 'For large organizations and complex projects',\n                                                    features: [\n                                                        'Full-Stack Solution',\n                                                        'Cloud Infrastructure',\n                                                        'AI/ML Integration',\n                                                        'Advanced Security',\n                                                        'Scalable Architecture',\n                                                        '12 Months Support',\n                                                        'DevOps Setup',\n                                                        'Training Included'\n                                                    ],\n                                                    popular: false,\n                                                    color: 'from-orange-500 to-red-600'\n                                                }\n                                            ].map(function(plan, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden \".concat(plan.popular ? 'border-2 border-cyan-400/50 scale-105' : ''),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8 text-center\",\n                                                        children: [\n                                                            plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-4 left-1/2 transform -translate-x-1/2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold\",\n                                                                    children: \"Most Popular\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1184,\n                                                                    columnNumber: 27\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1183,\n                                                                columnNumber: 25\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-3xl font-bold text-white mb-4\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1190,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-5xl font-black bg-gradient-to-r \".concat(plan.color, \" bg-clip-text text-transparent mb-2\"),\n                                                                        children: plan.price\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1193,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: plan.period\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1196,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1192,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-8\",\n                                                                children: plan.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1199,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-3 mb-8 text-left\",\n                                                                children: plan.features.map(function(feature, featureIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center text-gray-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-cyan-400 mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1204,\n                                                                                columnNumber: 29\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 1205,\n                                                                                columnNumber: 29\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1203,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1201,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                                onClick: function() {\n                                                                    var message = \"Hi! I'm interested in the \".concat(plan.name, \" package (\").concat(plan.price, \"). Can you provide more details?\");\n                                                                    window.open(\"https://wa.me/27794484159?text=\".concat(encodeURIComponent(message)), '_blank');\n                                                                },\n                                                                className: \"w-full text-lg py-4\",\n                                                                children: \"Get Started\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1210,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1175,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1116,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.4\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mt-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 text-lg mb-6\",\n                                                    children: \"Need a custom solution? We offer flexible pricing for unique requirements.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                                    onClick: function() {\n                                                        var _document_getElementById;\n                                                        return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    className: \"text-xl px-12 py-6 shadow-2xl\",\n                                                    children: \"Request Custom Quote\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 1224,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1100,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1088,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"contact\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80\",\n                                        alt: \"Contact Background\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1249,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1254,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1248,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow\",\n                                                children: \"GET IN TOUCH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1265,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                children: \"Ready to transform your business? Let's discuss your project and unlock your potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1268,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        title: 'Email Us',\n                                                        details: '<EMAIL>',\n                                                        subtitle: 'Get a detailed response within 24 hours',\n                                                        action: function() {\n                                                            return window.open('mailto:<EMAIL>', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                        title: 'WhatsApp Us',\n                                                        details: '+27 79 448 4159',\n                                                        subtitle: 'Instant messaging for quick queries',\n                                                        action: function() {\n                                                            return window.open('https://wa.me/27794484159', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                        title: 'Book a Call',\n                                                        details: 'Schedule a free consultation',\n                                                        subtitle: '30-minute strategy session',\n                                                        action: function() {\n                                                            return window.open('https://calendly.com/ampd-deviq', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                                        title: 'Based In',\n                                                        details: 'Cape Town, South Africa',\n                                                        subtitle: 'Serving clients globally'\n                                                    }\n                                                ].map(function(contact, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: contact.action,\n                                                        className: \"enhanced-card p-8 flex items-center space-x-6 \".concat(contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1316,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-white text-xl mb-2\",\n                                                                        children: contact.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 text-lg mb-1\",\n                                                                        children: contact.details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1321,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    contact.subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: contact.subtitle\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1319,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1311,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1275,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"enhanced-card p-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleContactSubmit,\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    placeholder: \"Your Name\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1340,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    placeholder: \"Your Email\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1347,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"company\",\n                                                            placeholder: \"Company Name\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1355,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            title: \"Budget Range\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Select Budget Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1366,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Under R50,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Under R50,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1367,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R50,000 - R150,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R50,000 - R150,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1368,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R150,000 - R300,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R150,000 - R300,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1369,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R300,000 - R500,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R300,000 - R500,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1370,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R500,000+\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R500,000+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1371,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1361,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 6,\n                                                            placeholder: \"Tell us about your project...\",\n                                                            required: true,\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1373,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50\",\n                                                            children: \"Send Message via WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 1380,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1331,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1257,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1246,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-16 px-8 border-t border-white/10 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                    alt: \"Footer Background\",\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 1395,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1394,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: \"/ampd-logo.png\",\n                                                                    alt: \"AMPD Dev-IQ\",\n                                                                    className: \"w-12 h-12 object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1408,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1407,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-black text-white\",\n                                                                        children: \"AMPD Dev-IQ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1411,\n                                                                        columnNumber: 21\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-cyan-400\",\n                                                                        children: \"Smart Software. Limitless Potential.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 21\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                        children: \"We are a Cape Town-based software development company specializing in custom web applications, mobile apps, cloud solutions, and AI/ML implementations. Transform your business with our cutting-edge technology solutions.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1415,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"https://wa.me/27794484159\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1428,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1421,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                whileHover: {\n                                                                    scale: 1.1\n                                                                },\n                                                                className: \"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-blue-500/30 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1435,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1430,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1420,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1442,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Web Development\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1444,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1444,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Mobile Apps\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1445,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1445,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Cloud Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1446,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1446,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"AI/ML Solutions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1447,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1447,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#services\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Enterprise Software\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1448,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1448,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1443,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-white font-bold text-lg mb-4\",\n                                                        children: \"Quick Links\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 17\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#about\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"About Us\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1456,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#portfolio\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Portfolio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1457,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"#contact\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Contact\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1458,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1458,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://wa.me/27794484159\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Get Quote\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1459,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 19\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: \"https://calendly.com/ampd-deviq\",\n                                                                    className: \"hover:text-cyan-400 transition-colors\",\n                                                                    children: \"Book Call\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 1460,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 19\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 1455,\n                                                        columnNumber: 17\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-white/10 pt-8 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-2\",\n                                                children: \"\\xa9 2024 Developed by AMPD Dev-IQ. All rights reserved.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1466,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 text-sm\",\n                                                children: \"Built with ❤️ and smart software for limitless potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 1469,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 1465,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1402,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1393,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        transition: {\n                            delay: 2,\n                            type: \"spring\",\n                            stiffness: 260,\n                            damping: 20\n                        },\n                        className: \"fixed bottom-6 right-6 z-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                            href: \"https://wa.me/27794484159?text=Hi! I'm interested in your software development services.\",\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white shadow-2xl hover:bg-green-600 transition-colors group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CalendarDaysIcon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-8 h-8 group-hover:animate-bounce\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 1491,\n                                columnNumber: 13\n                            }, _this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 1483,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 1477,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, _this);\n};\n_s(ModernSinglePage, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = ModernSinglePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSinglePage);\nvar _c;\n$RefreshReg$(_c, \"ModernSinglePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ModernSinglePage.tsx\n"));

/***/ })

});