'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon 
} from '@heroicons/react/24/outline';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    services: [
      { name: 'Web Development', href: '#services' },
      { name: 'Mobile Apps', href: '#services' },
      { name: 'Enterprise Software', href: '#services' },
      { name: 'AI/ML Solutions', href: '#services' },
      { name: 'Cloud Architecture', href: '#services' },
      { name: 'UI/UX Design', href: '#services' }
    ],
    company: [
      { name: 'About Us', href: '#about' },
      { name: 'Our Process', href: '#process' },
      { name: 'Portfolio', href: '#portfolio' },
      { name: 'Blog', href: '#blog' },
      { name: 'Careers', href: '/careers' },
      { name: 'Contact', href: '#contact' }
    ],
    resources: [
      { name: 'Case Studies', href: '#portfolio' },
      { name: 'Tech Blog', href: '#blog' },
      { name: 'Documentation', href: '/docs' },
      { name: 'API Reference', href: '/api' },
      { name: 'Support Center', href: '/support' },
      { name: 'Downloads', href: '/downloads' }
    ],
    legal: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Cookie Policy', href: '/cookies' },
      { name: 'GDPR Compliance', href: '/gdpr' }
    ]
  };

  const socialLinks = [
    { name: 'LinkedIn', href: 'https://linkedin.com/company/ampd-deviq', icon: '💼' },
    { name: 'Twitter', href: 'https://twitter.com/ampddeviq', icon: '🐦' },
    { name: 'GitHub', href: 'https://github.com/ampd-deviq', icon: '💻' },
    { name: 'Dribbble', href: 'https://dribbble.com/ampddeviq', icon: '🎨' },
    { name: 'YouTube', href: 'https://youtube.com/ampddeviq', icon: '📺' }
  ];

  const contactInfo = [
    {
      icon: EnvelopeIcon,
      text: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: PhoneIcon,
      text: '+****************',
      href: 'tel:+15551234567'
    },
    {
      icon: MapPinIcon,
      text: '123 Tech Street, Silicon Valley, CA 94000',
      href: 'https://maps.google.com'
    }
  ];

  return (
    <footer className="bg-gradient-to-br from-slate-900 via-gray-900 to-black text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-blue-600/10 to-purple-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-cyan-600/10 to-pink-600/10 rounded-full blur-3xl"></div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-6 py-20 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              {/* Enhanced Logo */}
              <Link href="/" className="flex items-center space-x-3 mb-6 group">
                <div className="w-12 h-12 bg-gradient-to-r from-cyan-400/20 to-purple-600/20 rounded-xl flex items-center justify-center backdrop-blur-xl border border-cyan-400/30 group-hover:scale-105 transition-all duration-300">
                  <img
                    src="/logo.png"
                    alt="AMPD Dev-IQ Logo"
                    className="w-8 h-8 object-contain filter brightness-110"
                    onError={(e) => {
                      // Fallback to styled letter if logo fails to load
                      e.currentTarget.style.display = 'none';
                      const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                      if (nextElement) {
                        nextElement.style.display = 'flex';
                      }
                    }}
                  />
                  <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-lg flex items-center justify-center hidden">
                    <span className="text-white font-black text-sm">A</span>
                  </div>
                </div>
                <div className="flex flex-col">
                  <span className="text-2xl font-black font-poppins">
                    <span className="bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">AMPD</span>
                    <span className="text-white ml-1">Dev-IQ</span>
                  </span>
                  <span className="text-cyan-400 text-sm font-semibold">Smart Software. Limitless Potential.</span>
                </div>
              </Link>

              <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                We amplify your business with intelligent software solutions.
                <span className="text-cyan-400 font-semibold"> Transforming complex challenges into limitless opportunities</span> that drive exponential growth.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                {contactInfo.map((info, index) => (
                  <Link
                    key={index}
                    href={info.href}
                    className="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors group"
                  >
                    <info.icon className="w-5 h-5 text-blue-400 group-hover:text-blue-300" />
                    <span className="text-sm">{info.text}</span>
                  </Link>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Services */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold font-poppins mb-8 text-cyan-400 neon-glow">SERVICES</h3>
            <ul className="space-y-3">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Company */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold font-poppins mb-8 text-purple-400 neon-glow">COMPANY</h3>
            <ul className="space-y-3">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Resources */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold font-poppins mb-8 text-green-400 neon-glow">RESOURCES</h3>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Legal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold font-poppins mb-8 text-pink-400 neon-glow">LEGAL</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-12 pt-8 border-t border-gray-800"
        >
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="text-4xl font-bold font-poppins mb-6 text-white neon-glow">STAY UPDATED</h3>
            <p className="text-xl text-gray-200 mb-8">
              Get the latest insights on <span className="text-cyan-400 font-semibold">technology trends</span> and
              <span className="text-purple-400 font-semibold"> development best practices</span>.
            </p>
            <div className="flex gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-6 py-4 bg-white/10 border-2 border-white/30 rounded-2xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 text-white text-lg placeholder-gray-300 backdrop-blur-md"
              />
              <button type="submit" className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-cyan-500/50 border-2 border-cyan-400/30">
                SUBSCRIBE
              </button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <motion.p
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-gray-400 text-sm mb-4 md:mb-0"
            >
              © {currentYear} Developed by AMPD Dev-IQ. All rights reserved. Built with ❤️ and smart software for limitless potential.
            </motion.p>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="flex space-x-4"
            >
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors group"
                  title={social.name}
                >
                  <span className="text-lg group-hover:scale-110 transition-transform">
                    {social.icon}
                  </span>
                </Link>
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
