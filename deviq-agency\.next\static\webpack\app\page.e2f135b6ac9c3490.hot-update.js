"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ModernSinglePage.tsx":
/*!*****************************************!*\
  !*** ./components/ModernSinglePage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AdvancedAnimations */ \"(app-pages-browser)/./components/AdvancedAnimations.tsx\");\n/* harmony import */ var _MovingGraphics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MovingGraphics */ \"(app-pages-browser)/./components/MovingGraphics.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DevicePhoneMobileIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,Bars3Icon,CheckCircleIcon,CloudIcon,CodeBracketIcon,CpuChipIcon,DevicePhoneMobileIcon,EnvelopeIcon,MapPinIcon,PhoneIcon,PlayIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar ModernSinglePage = function() {\n    _s();\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isMenuOpen = _useState[0], setIsMenuOpen = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isScrolled = _useState1[0], setIsScrolled = _useState1[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModernSinglePage.useEffect\": function() {\n            var handleScroll = {\n                \"ModernSinglePage.useEffect.handleScroll\": function() {\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ModernSinglePage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ModernSinglePage.useEffect\": function() {\n                    return window.removeEventListener('scroll', handleScroll);\n                }\n            })[\"ModernSinglePage.useEffect\"];\n        }\n    }[\"ModernSinglePage.useEffect\"], []);\n    var services = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Custom Web Development',\n            description: 'Modern, responsive websites and web applications built with cutting-edge technologies for optimal performance and user experience.',\n            features: [\n                'React/Next.js',\n                'Node.js',\n                'TypeScript',\n                'Progressive Web Apps',\n                'E-commerce Solutions',\n                'CMS Development'\n            ],\n            image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R15,000',\n            timeline: '2-8 weeks'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: 'Mobile App Development',\n            description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences.',\n            features: [\n                'React Native',\n                'Flutter',\n                'iOS/Android Native',\n                'App Store Optimization',\n                'Push Notifications',\n                'Offline Functionality'\n            ],\n            image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R25,000',\n            timeline: '4-12 weeks'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Cloud & DevOps Solutions',\n            description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions for enterprise-grade applications.',\n            features: [\n                'AWS/Azure/GCP',\n                'Docker/Kubernetes',\n                'CI/CD Pipelines',\n                'Monitoring & Analytics',\n                'Auto-scaling',\n                'Security'\n            ],\n            image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R20,000',\n            timeline: '3-10 weeks'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'AI/ML Solutions',\n            description: 'Intelligent systems powered by artificial intelligence and machine learning for data-driven business insights.',\n            features: [\n                'Natural Language Processing',\n                'Computer Vision',\n                'Predictive Analytics',\n                'Chatbots',\n                'Data Mining',\n                'Deep Learning'\n            ],\n            image: 'https://images.unsplash.com/photo-1555255707-c07966088b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R30,000',\n            timeline: '6-16 weeks'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Enterprise Software',\n            description: 'Custom enterprise solutions including ERP, CRM, and workflow automation systems for large organizations.',\n            features: [\n                'ERP Systems',\n                'CRM Solutions',\n                'Workflow Automation',\n                'Data Analytics',\n                'Integration APIs',\n                'Security Compliance'\n            ],\n            image: 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R50,000',\n            timeline: '8-24 weeks'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            title: 'Digital Transformation',\n            description: 'Complete digital transformation services to modernize your business processes and technology infrastructure.',\n            features: [\n                'Process Automation',\n                'Legacy System Migration',\n                'Digital Strategy',\n                'Change Management',\n                'Training & Support',\n                'ROI Analysis'\n            ],\n            image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',\n            price: 'From R75,000',\n            timeline: '12-36 weeks'\n        }\n    ];\n    var stats = [\n        {\n            number: '500+',\n            label: 'Projects Delivered'\n        },\n        {\n            number: '50+',\n            label: 'Happy Clients'\n        },\n        {\n            number: '5+',\n            label: 'Years Experience'\n        },\n        {\n            number: '24/7',\n            label: 'Support'\n        }\n    ];\n    var testimonials = [\n        {\n            name: 'Sarah Johnson',\n            position: 'CEO',\n            company: 'TechStore Inc.',\n            image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n            text: 'AMPD Dev-IQ transformed our online presence completely. Their e-commerce platform increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',\n            rating: 5,\n            project: 'E-Commerce Platform'\n        },\n        {\n            name: 'Dr. Michael Chen',\n            position: 'Chief Medical Officer',\n            company: 'HealthCare Plus',\n            image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',\n            text: 'The healthcare management system has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',\n            rating: 5,\n            project: 'Healthcare Management System'\n        },\n        {\n            name: 'Jennifer Williams',\n            position: 'Digital Banking Director',\n            company: 'SecureBank',\n            image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',\n            text: 'Our customers love the new mobile banking app. It\\'s intuitive, secure, and feature-rich. AMPD Dev-IQ delivered exactly what we needed to compete in the digital banking space.',\n            rating: 5,\n            project: 'Mobile Banking App'\n        },\n        {\n            name: 'David Rodriguez',\n            position: 'Operations Manager',\n            company: 'LogiFlow Corp',\n            image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            text: 'The logistics management system streamlined our entire operation. Real-time tracking and automated workflows have reduced our operational costs by 40%.',\n            rating: 5,\n            project: 'Logistics Management System'\n        }\n    ];\n    var portfolio = [\n        {\n            id: 1,\n            title: 'E-Commerce Platform',\n            category: 'Web Development',\n            description: 'A modern e-commerce platform with AI-powered recommendations and real-time inventory management.',\n            image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400',\n            technologies: [\n                'React',\n                'Node.js',\n                'PostgreSQL',\n                'AWS'\n            ],\n            impact: '300% increase in sales, 50% reduction in cart abandonment'\n        },\n        {\n            id: 2,\n            title: 'Healthcare Management System',\n            category: 'Enterprise Software',\n            description: 'Comprehensive healthcare management system with patient records, appointment scheduling, and telemedicine.',\n            image: 'https://images.unsplash.com/photo-*************-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400',\n            technologies: [\n                'React',\n                'Python',\n                'MongoDB',\n                'Docker'\n            ],\n            impact: '60% reduction in administrative time, improved patient satisfaction'\n        },\n        {\n            id: 3,\n            title: 'Mobile Banking App',\n            category: 'Mobile Development',\n            description: 'Secure mobile banking application with biometric authentication and real-time transactions.',\n            image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400',\n            technologies: [\n                'React Native',\n                'Node.js',\n                'PostgreSQL',\n                'AWS'\n            ],\n            impact: '2M+ downloads, 4.8 star rating, 95% user retention'\n        }\n    ];\n    var handleContactSubmit = function(e) {\n        e.preventDefault();\n        var formData = new FormData(e.target);\n        var message = \"Hi! I'm \".concat(formData.get('name'), \" from \").concat(formData.get('company'), \".\\n\\nEmail: \").concat(formData.get('email'), \"\\nBudget: \").concat(formData.get('budget'), \"\\n\\nProject Details:\\n\").concat(formData.get('message'), \"\\n\\nI'd like to discuss this project with you.\");\n        var whatsappUrl = \"https://wa.me/***********?text=\".concat(encodeURIComponent(message));\n        window.open(whatsappUrl, '_blank');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.FloatingParticles, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.MorphingBackground, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.MovingShapes, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.CursorFollower, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.AnimatedGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.FloatingTechIcons, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.ParticleSystem, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 150,\n                color: \"#22d3ee\",\n                className: \"top-20 right-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 100,\n                color: \"#a855f7\",\n                className: \"bottom-40 left-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.GlowingOrb, {\n                size: 80,\n                color: \"#f97316\",\n                className: \"top-1/2 left-1/3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\",\n                                alt: \"Software Development Background\",\n                                className: \"w-full h-full object-cover opacity-15\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900/98 via-purple-900/95 to-slate-900/98\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                            alt: \"Tech Animation\",\n                            className: \"w-full h-full object-cover mix-blend-overlay\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"https://i.pinimg.com/originals/36/e4/d0/36e4d0b856694fc471344b644a1dd6e4.gif\",\n                            alt: \"Tech Animation\",\n                            className: \"w-full h-full object-cover mix-blend-screen\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.header, {\n                        initial: {\n                            y: -100\n                        },\n                        animate: {\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"container mx-auto px-8 py-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/ampd-logo.png\",\n                                                        alt: \"AMPD Dev-IQ\",\n                                                        className: \"w-12 h-12 object-contain\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-black text-white\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-cyan-400\",\n                                                            children: \"Smart Software. Limitless Potential.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#services\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Services\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#about\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#portfolio\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Portfolio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#contact\",\n                                                    className: \"text-white/80 hover:text-white transition-colors\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: function() {\n                                                return setIsMenuOpen(!isMenuOpen);\n                                            },\n                                            className: \"lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 19\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, _this),\n                                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: \"lg:hidden mt-4 py-4 border-t border-white/20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#services\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#portfolio\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Portfolio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-white/80 hover:text-white transition-colors py-2\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"min-h-screen flex items-center justify-center px-8 pt-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.1\n                                    },\n                                    className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-7 h-7 text-cyan-400 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg neon-glow\",\n                                            children: \"Trusted by 500+ Companies Worldwide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.InteractiveBlob, {\n                                            className: \"absolute inset-0 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"SMART SOFTWARE.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                            delay: 0.2\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.GlitchText, {\n                                            text: \"LIMITLESS\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.AnimatedText, {\n                                            text: \"POTENTIAL.\",\n                                            className: \"block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent\",\n                                            delay: 0.6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed\",\n                                    children: [\n                                        \"We transform your \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-cyan-400 font-semibold\",\n                                            children: \"business ideas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 33\n                                        }, _this),\n                                        \" into\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-purple-400 font-semibold\",\n                                            children: \" powerful digital solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, _this),\n                                        \" that drive growth and innovation.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"flex flex-col md:flex-row gap-6 justify-center items-center mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.LiquidButton, {\n                                            onClick: function() {\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: 'smooth'\n                                                });\n                                            },\n                                            className: \"text-xl px-12 py-6 shadow-2xl\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Start Your Project\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.a, {\n                                            href: \"#about\",\n                                            className: \"group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3\",\n                                            whileHover: {\n                                                scale: 1.05,\n                                                boxShadow: \"0 0 30px rgba(34, 211, 238, 0.5)\"\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"w-4 h-4 ml-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Watch Demo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                                    children: stats.map(function(stat, index) {\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 19\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-300 font-semibold\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, _this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, _this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"services\",\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Team Collaboration\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 13\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 11\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR SERVICES\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 15\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Comprehensive digital solutions to transform your business with cutting-edge technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 15\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 13\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\",\n                                            children: services.map(function(service, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 mb-6 rounded-2xl overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: service.image,\n                                                                        alt: service.title,\n                                                                        className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 497,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 502,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute bottom-4 left-4 right-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between text-white\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold bg-cyan-500/20 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                                    children: service.price\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 27\n                                                                                }, _this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm bg-purple-500/20 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                                    children: service.timeline\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                    lineNumber: 508,\n                                                                                    columnNumber: 27\n                                                                                }, _this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-2xl shadow-cyan-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(service.icon, {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-2xl font-bold text-white group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                            children: service.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 25\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-6 leading-relaxed group-hover:text-gray-200 transition-colors duration-300\",\n                                                                children: service.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-3 mb-6\",\n                                                                children: service.features.slice(0, 4).map(function(feature, featureIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        className: \"flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"w-5 h-5 text-cyan-400 mr-3 group-hover:text-cyan-300 transition-colors duration-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 535,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm\",\n                                                                                children: feature\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, featureIndex, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 25\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                                                whileHover: {\n                                                                    scale: 1.05\n                                                                },\n                                                                whileTap: {\n                                                                    scale: 0.95\n                                                                },\n                                                                className: \"w-full bg-gradient-to-r from-cyan-500/20 to-purple-600/20 hover:from-cyan-500/30 hover:to-purple-600/30 border border-cyan-400/30 text-white font-semibold py-3 rounded-xl transition-all duration-300\",\n                                                                onClick: function() {\n                                                                    var _document_getElementById;\n                                                                    return (_document_getElementById = document.getElementById('contact')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                                        behavior: 'smooth'\n                                                                    });\n                                                                },\n                                                                children: \"Get Quote\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 13\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 11\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            id: \"portfolio\",\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Portfolio Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-cyan-900/80 to-purple-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 568,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"OUR PORTFOLIO\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Showcasing our successful projects and the impact we've made for our clients\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                            children: portfolio.map(function(project, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"group enhanced-card hover:border-cyan-400/50 relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative h-48 mb-6 rounded-2xl overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: project.image,\n                                                                        alt: project.title,\n                                                                        className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 to-transparent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-4 left-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-semibold bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                                            children: project.category\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 27\n                                                                        }, _this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300\",\n                                                                children: project.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                                children: project.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2 mb-6\",\n                                                                children: project.technologies.map(function(tech, techIndex) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-lg\",\n                                                                        children: tech\n                                                                    }, techIndex, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"border-t border-white/10 pt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-cyan-400 font-semibold mb-2\",\n                                                                        children: \"Impact:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 636,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-400\",\n                                                                        children: project.impact\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 637,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, project.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"about\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80\",\n                                        alt: \"Team Working\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 651,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-900/80 to-orange-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-content\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"ABOUT US\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 668,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 leading-relaxed\",\n                                                    children: [\n                                                        \"At \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-cyan-400 font-bold ampd-glow\",\n                                                            children: \"AMPD Dev-IQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 22\n                                                        }, _this),\n                                                        \", we believe that smart software has the power to unlock limitless potential in every business. Our mission is to amplify your success through intelligent solutions and innovative technology.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-10\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-cyan-400 mb-4 neon-glow\",\n                                                                    children: \"98%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"Client Satisfaction\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center enhanced-card p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-5xl lg:text-6xl font-black text-purple-400 mb-4 neon-glow\",\n                                                                    children: \"100%\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 681,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xl text-gray-300\",\n                                                                    children: \"On-Time Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 50\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"grid grid-cols-2 gap-6\",\n                                            children: [\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                    title: 'Innovation',\n                                                    desc: 'Cutting-edge solutions'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                    title: 'Quality',\n                                                    desc: 'Premium standards'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    title: 'Reliability',\n                                                    desc: 'Trusted delivery'\n                                                },\n                                                {\n                                                    icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                    title: 'Excellence',\n                                                    desc: 'Outstanding results'\n                                                }\n                                            ].map(function(item, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 702,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-white mb-2\",\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 21\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: item.desc\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 21\n                                                        }, _this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 659,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 648,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdvancedAnimations__WEBPACK_IMPORTED_MODULE_2__.ParallaxSection, {\n                        speed: 0.2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"py-32 px-8 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\",\n                                            alt: \"Testimonials Background\",\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-green-900/80 to-cyan-900/80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-7xl mx-auto relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"text-center mb-24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow\",\n                                                    children: \"CLIENT TESTIMONIALS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                    children: \"Hear what our satisfied clients say about our work and results\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: testimonials.map(function(testimonial, index) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MovingGraphics__WEBPACK_IMPORTED_MODULE_3__.HolographicCard, {\n                                                    className: \"enhanced-card relative overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 30\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.8,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"p-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-6\",\n                                                                children: (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_19__._)(Array(testimonial.rating)).map(function(_, i) {\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-6 h-6 text-yellow-400 fill-current\"\n                                                                    }, i, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 27\n                                                                    }, _this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-200 text-lg mb-8 italic leading-relaxed\",\n                                                                children: [\n                                                                    '\"',\n                                                                    testimonial.text,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: testimonial.image,\n                                                                        alt: testimonial.name,\n                                                                        className: \"w-16 h-16 rounded-full object-cover mr-4 border-2 border-cyan-400/30\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 25\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-bold text-white text-lg\",\n                                                                                children: testimonial.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 775,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-cyan-400 text-sm\",\n                                                                                children: testimonial.position\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 776,\n                                                                                columnNumber: 27\n                                                                            }, _this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: testimonial.company\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                                lineNumber: 777,\n                                                                                columnNumber: 27\n                                                                            }, _this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 25\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 23\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-6 pt-6 border-t border-white/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full\",\n                                                                    children: [\n                                                                        \"Project: \",\n                                                                        testimonial.project\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 783,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 782,\n                                                                columnNumber: 23\n                                                            }, _this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 21\n                                                    }, _this)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 19\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"contact\",\n                        className: \"py-32 px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80\",\n                                        alt: \"Contact Background\",\n                                        className: \"w-full h-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 804,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto relative z-10\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow\",\n                                                children: \"GET IN TOUCH\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed\",\n                                                children: \"Ready to transform your business? Let's discuss your project and unlock your potential.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 13\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                                        title: 'Email Us',\n                                                        details: '<EMAIL>',\n                                                        action: function() {\n                                                            return window.open('mailto:<EMAIL>', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                                        title: 'WhatsApp Us',\n                                                        details: '+27 79 448 4159',\n                                                        action: function() {\n                                                            return window.open('https://wa.me/***********', '_blank');\n                                                        }\n                                                    },\n                                                    {\n                                                        icon: _barrel_optimize_names_ArrowRightIcon_Bars3Icon_CheckCircleIcon_CloudIcon_CodeBracketIcon_CpuChipIcon_DevicePhoneMobileIcon_EnvelopeIcon_MapPinIcon_PhoneIcon_PlayIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                                        title: 'Based In',\n                                                        details: 'Cape Town, South Africa'\n                                                    }\n                                                ].map(function(contact, index) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: contact.action,\n                                                        className: \"enhanced-card p-8 flex items-center space-x-6 \".concat(contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contact.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 857,\n                                                                    columnNumber: 23\n                                                                }, _this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 21\n                                                            }, _this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-bold text-white text-xl mb-2\",\n                                                                        children: contact.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 860,\n                                                                        columnNumber: 23\n                                                                    }, _this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-gray-300 text-lg\",\n                                                                        children: contact.details\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 23\n                                                                    }, _this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 21\n                                                            }, _this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, _this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 15\n                                            }, _this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 50\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                className: \"enhanced-card p-10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                    onSubmit: handleContactSubmit,\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"name\",\n                                                                    placeholder: \"Your Name\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 877,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    name: \"email\",\n                                                                    placeholder: \"Your Email\",\n                                                                    required: true,\n                                                                    className: \"bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 884,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            name: \"company\",\n                                                            placeholder: \"Company Name\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 892,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            name: \"budget\",\n                                                            title: \"Budget Range\",\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Select Budget Range\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Under R50,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"Under R50,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R50,000 - R150,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R50,000 - R150,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R150,000 - R300,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R150,000 - R300,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R300,000 - R500,000\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R300,000 - R500,000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 907,\n                                                                    columnNumber: 21\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"R500,000+\",\n                                                                    className: \"bg-slate-800\",\n                                                                    children: \"R500,000+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                                    lineNumber: 908,\n                                                                    columnNumber: 21\n                                                                }, _this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            name: \"message\",\n                                                            rows: 6,\n                                                            placeholder: \"Tell us about your project...\",\n                                                            required: true,\n                                                            className: \"w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 19\n                                                        }, _this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            className: \"w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50\",\n                                                            children: \"Send Message via WhatsApp\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                            lineNumber: 917,\n                                                            columnNumber: 19\n                                                        }, _this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 15\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 13\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                lineNumber: 807,\n                                columnNumber: 11\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 796,\n                        columnNumber: 9\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"py-16 px-8 border-t border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/ampd-logo.png\",\n                                                alt: \"AMPD Dev-IQ\",\n                                                className: \"w-8 h-8 object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 17\n                                            }, _this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xl font-black text-white\",\n                                                    children: \"AMPD Dev-IQ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-cyan-400\",\n                                                    children: \"Smart Software. Limitless Potential.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                                    lineNumber: 938,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 mb-4\",\n                                    children: \"\\xa9 2024 Developed by AMPD Dev-IQ. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: \"Built with ❤️ and smart software for limitless potential.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                                    lineNumber: 944,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 11\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 9\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\DevIQ\\\\deviq-agency\\\\components\\\\ModernSinglePage.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, _this);\n};\n_s(ModernSinglePage, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = ModernSinglePage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSinglePage);\nvar _c;\n$RefreshReg$(_c, \"ModernSinglePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ModernSinglePage.tsx\n"));

/***/ })

});