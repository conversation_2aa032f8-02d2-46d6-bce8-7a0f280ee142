'use client';

import { motion } from 'framer-motion';
import { 
  CodeBracketIcon, 
  DevicePhoneMobileIcon, 
  BuildingOfficeIcon,
  CloudIcon,
  CpuChipIcon,
  PaintBrushIcon,
  CogIcon
} from '@heroicons/react/24/outline';

const Services = () => {
  const services = [
    {
      icon: CodeBracketIcon,
      title: 'Custom Web Development',
      description: 'Modern, responsive websites and web applications built with cutting-edge technologies.',
      features: ['React/Next.js', 'Node.js', 'TypeScript', 'Progressive Web Apps'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: DevicePhoneMobileIcon,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications for iOS and Android.',
      features: ['React Native', 'Flutter', 'iOS/Android Native', 'App Store Optimization'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: BuildingOfficeIcon,
      title: 'Enterprise Software',
      description: 'Scalable enterprise solutions that streamline business operations.',
      features: ['ERP Systems', 'CRM Solutions', 'Workflow Automation', 'Data Analytics'],
      color: 'from-green-500 to-green-600'
    },
    {
      icon: CogIcon,
      title: 'API Integrations',
      description: 'Seamless integration of third-party services and custom API development.',
      features: ['REST APIs', 'GraphQL', 'Microservices', 'Payment Gateways'],
      color: 'from-orange-500 to-orange-600'
    },
    {
      icon: CloudIcon,
      title: 'Cloud Architecture',
      description: 'Robust cloud infrastructure and DevOps solutions for scalable applications.',
      features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring'],
      color: 'from-cyan-500 to-cyan-600'
    },
    {
      icon: CpuChipIcon,
      title: 'AI/ML Solutions',
      description: 'Intelligent solutions powered by artificial intelligence and machine learning.',
      features: ['Natural Language Processing', 'Computer Vision', 'Predictive Analytics', 'Chatbots'],
      color: 'from-pink-500 to-pink-600'
    },
    {
      icon: PaintBrushIcon,
      title: 'UI/UX Design',
      description: 'Beautiful, intuitive designs that provide exceptional user experiences.',
      features: ['User Research', 'Wireframing', 'Prototyping', 'Design Systems'],
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section id="services" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
          {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 backdrop-blur-md border border-cyan-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20">
            <span className="text-cyan-400 font-semibold text-2xl">Our Services</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent drop-shadow-2xl">
              SERVICES
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            We offer <span className="text-cyan-400 font-semibold">comprehensive software development services</span> to help businesses
            <span className="text-purple-400 font-semibold"> thrive in the digital age</span>.
          </p>
        </motion.div>

        {/* Enhanced Services Grid - Full Width */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-12 lg:gap-16"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-xl border-2 border-white/40 rounded-3xl p-12 lg:p-16 hover:bg-gradient-to-br hover:from-white/25 hover:to-white/10 transition-all duration-700 transform hover:-translate-y-8 hover:scale-105 hover:shadow-2xl hover:shadow-cyan-500/40 hover:border-cyan-400/60"
            >
              {/* Enhanced Icon */}
              <div className={`w-32 h-32 lg:w-40 lg:h-40 bg-gradient-to-r ${service.color} rounded-3xl flex items-center justify-center mb-10 group-hover:scale-110 transition-transform duration-500 shadow-2xl shadow-black/30`}>
                <service.icon className="w-16 h-16 lg:w-20 lg:h-20 text-white" />
              </div>

              {/* Enhanced Content */}
              <h3 className="text-5xl lg:text-6xl font-black font-poppins mb-10 text-white neon-glow leading-tight">
                {service.title.toUpperCase()}
              </h3>
              <p className="text-2xl lg:text-3xl text-gray-200 mb-12 leading-relaxed">
                {service.description}
              </p>

              {/* Enhanced Features */}
              <ul className="space-y-6">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-xl lg:text-2xl text-gray-300">
                    <div className={`w-6 h-6 bg-gradient-to-r ${service.color} rounded-full mr-6 shadow-lg flex-shrink-0`}></div>
                    <span className="font-medium">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* Enhanced Hover Effect */}
              <div className="mt-12 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-6 group-hover:translate-y-0">
                <button type="button" className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-400 hover:to-blue-500 text-white font-bold text-2xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-2xl flex items-center space-x-4 w-full justify-center">
                  <span>LEARN MORE</span>
                  <span className="text-3xl">→</span>
                </button>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-32"
        >
          <p className="text-4xl md:text-5xl lg:text-6xl text-gray-200 mb-16 leading-relaxed font-light">
            Ready to <span className="text-cyan-400 font-bold">transform your business</span> with
            <span className="text-purple-400 font-bold"> cutting-edge technology</span>?
          </p>
          <button type="button" className="bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-black text-4xl lg:text-5xl px-20 py-10 rounded-3xl transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-cyan-500/50 border-4 border-cyan-400/40 neon-glow">
            DISCUSS YOUR PROJECT
          </button>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Services;
