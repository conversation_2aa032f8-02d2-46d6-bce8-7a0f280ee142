'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, PlayIcon, SparklesIcon } from '@heroicons/react/24/outline';

const Hero = () => {
  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Dynamic Animated Background with Featured GIF */}
      <div className="absolute inset-0 z-0">
        {/* Featured Animated GIF Background */}
        <div className="absolute inset-0 opacity-25">
          <img
            src="https://i.pinimg.com/originals/36/e4/d0/36e4d0b856694fc471344b644a1dd6e4.gif"
            alt="Tech Animation"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Multiple Background Images Grid */}
        <div className="absolute inset-0 grid grid-cols-4 grid-rows-3 gap-0 opacity-15">
          <img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=600&h=400&fit=crop" alt="Code" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600&h=400&fit=crop" alt="Programming" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=600&h=400&fit=crop" alt="Development" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=600&h=400&fit=crop" alt="Tech" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop" alt="Coding" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop" alt="Data" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop" alt="Software" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1573164713714-d95e436ab8d6?w=600&h=400&fit=crop" alt="AI" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?w=600&h=400&fit=crop" alt="Mobile" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop" alt="Web" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop" alt="Database" className="w-full h-full object-cover animate-pulse" />
          <img src="https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop" alt="Analytics" className="w-full h-full object-cover animate-pulse" />
        </div>

        {/* Neon Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/80 via-purple-900/90 to-black/80"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-cyan-500/20 via-transparent to-pink-500/20 animate-pulse"></div>
        <div className="absolute inset-0 bg-gradient-to-bl from-blue-500/15 via-transparent to-green-500/15 animate-pulse animate-delay-1"></div>

        {/* Massive Neon Floating Elements */}
        <div className="absolute top-10 left-10 w-64 h-64 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full opacity-20 animate-pulse blur-3xl"></div>
        <div className="absolute top-20 right-20 w-48 h-48 bg-gradient-to-br from-pink-400 to-purple-600 rounded-full opacity-25 animate-pulse blur-2xl animate-delay-1"></div>
        <div className="absolute bottom-20 left-32 w-80 h-80 bg-gradient-to-br from-green-400 to-cyan-600 rounded-full opacity-15 animate-pulse blur-3xl animate-delay-2"></div>
        <div className="absolute bottom-32 right-16 w-56 h-56 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full opacity-20 animate-pulse blur-2xl animate-delay-0-5"></div>

        {/* Giant Code Elements */}
        <div className="absolute top-1/4 left-1/6 text-cyan-400/30 text-9xl font-mono animate-pulse animate-delay-3 transform rotate-12">{'<>'}</div>
        <div className="absolute bottom-1/4 right-1/6 text-pink-400/30 text-8xl font-mono animate-pulse animate-delay-1-5 transform -rotate-12">{'{ }'}</div>
        <div className="absolute top-1/2 right-1/4 text-green-400/30 text-7xl font-mono animate-pulse animate-delay-2-5 transform rotate-6">{'</>'}</div>
        <div className="absolute top-1/3 left-1/3 text-purple-400/25 text-6xl font-mono animate-pulse animate-delay-4 transform -rotate-6">{'[]'}</div>
      </div>

      <div className="container mx-auto px-8 lg:px-12 relative z-10">
        <div className="text-center w-full">
          {/* Badge - Much Larger */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="inline-flex items-center space-x-6 bg-gradient-to-r from-cyan-500/20 to-purple-500/20 backdrop-blur-md border border-cyan-400/30 rounded-full px-12 py-6 lg:px-16 lg:py-8 mb-16 lg:mb-20 shadow-2xl shadow-cyan-500/20"
          >
            <SparklesIcon className="w-10 h-10 lg:w-12 lg:h-12 text-cyan-400" />
            <span className="text-white font-semibold text-2xl lg:text-3xl xl:text-4xl">Trusted by 500+ Companies Worldwide</span>
          </motion.div>

          {/* ULTRA MASSIVE Main Headline with New Branding */}
          <motion.h1
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-ultra font-black font-poppins mb-12 leading-none tracking-tight neon-glow-strong"
          >
            <span className="block bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent drop-shadow-2xl">
              SMART SOFTWARE.
            </span>
            <span className="block bg-gradient-to-r from-purple-400 via-pink-500 to-indigo-600 bg-clip-text text-transparent drop-shadow-2xl">
              LIMITLESS
            </span>
            <span className="block bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent drop-shadow-2xl">
              POTENTIAL.
            </span>
          </motion.h1>

          {/* Enhanced Brand Tagline */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="mb-8"
          >
            <div className="inline-flex items-center space-x-6 bg-gradient-to-r from-cyan-500/10 to-purple-500/10 backdrop-blur-xl border-2 border-cyan-400/30 rounded-full px-12 py-6 shadow-2xl">
              <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-black text-xl">A</span>
              </div>
              <span className="text-4xl font-black font-poppins">
                <span className="bg-gradient-to-r from-cyan-400 to-purple-600 bg-clip-text text-transparent">AMPD</span>
                <span className="text-white ml-2">Dev-IQ</span>
              </span>
            </div>
          </motion.div>

          {/* Large Subheading - Much Bigger */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-gray-200 mb-20 lg:mb-24 max-w-none mx-auto leading-relaxed font-light px-4"
          >
            Amplifying your business with <span className="text-cyan-400 font-semibold">intelligent software solutions</span> that unlock
            <span className="text-purple-400 font-semibold"> unlimited growth potential</span>. We transform complex challenges into
            <span className="text-pink-400 font-semibold"> elegant digital experiences</span>.
          </motion.p>

          {/* MASSIVE CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col lg:flex-row gap-8 justify-center items-center mb-20"
          >
            <Link
              href="#contact"
              className="group relative bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-3xl px-16 py-8 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-cyan-500/50 flex items-center space-x-4 border-2 border-cyan-400/30"
            >
              <span>REQUEST FREE PROPOSAL</span>
              <ArrowRightIcon className="w-8 h-8 group-hover:translate-x-2 transition-transform duration-300" />
            </Link>
            <Link
              href="#about"
              className="group bg-gradient-to-r from-pink-500/20 to-purple-500/20 backdrop-blur-md border-2 border-pink-400/40 hover:bg-gradient-to-r hover:from-pink-500/30 hover:to-purple-500/30 text-white font-bold text-3xl px-16 py-8 rounded-2xl transition-all duration-300 transform hover:scale-110 flex items-center space-x-4 shadow-2xl hover:shadow-pink-500/50"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full flex items-center justify-center">
                <PlayIcon className="w-8 h-8 ml-1" />
              </div>
              <span>MEET AMPD DEV-IQ</span>
            </Link>
          </motion.div>

          {/* Center Hero Image - Much Larger */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.7 }}
            className="mb-24 lg:mb-32 flex justify-center"
          >
            <div className="relative w-[500px] h-[500px] lg:w-[600px] lg:h-[600px] xl:w-[700px] xl:h-[700px] rounded-3xl overflow-hidden shadow-2xl shadow-cyan-500/30 border-4 border-cyan-400/40 bg-gradient-to-br from-cyan-500/20 to-purple-600/20 backdrop-blur-xl">
              <img
                src="/center-hero-image.jpg"
                alt="AMPD Dev-IQ Hero"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(1.1) contrast(1.1) drop-shadow(0 0 20px rgba(34, 211, 238, 0.3))'
                }}
                onError={(e) => {
                  // Fallback gradient if image fails to load
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              <div className="w-full h-full bg-gradient-to-br from-cyan-400/30 to-purple-600/30 flex items-center justify-center hidden">
                <div className="text-center">
                  <div className="text-6xl font-black text-white mb-4">AMPD</div>
                  <div className="text-2xl text-cyan-400">Dev-IQ</div>
                </div>
              </div>
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/10 to-purple-600/10 animate-pulse"></div>
            </div>
          </motion.div>

          {/* Enhanced Company Logos - Much Larger */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mb-24 lg:mb-32"
          >
            <p className="text-gray-300 text-3xl lg:text-4xl mb-16 lg:mb-20 uppercase tracking-wider font-semibold">Trusted by industry leaders</p>
            <div className="flex flex-wrap justify-center items-center gap-16 lg:gap-20">
              <div className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 backdrop-blur-md rounded-3xl px-12 py-8 lg:px-16 lg:py-10 border border-cyan-400/30 shadow-xl">
                <span className="text-white font-bold text-3xl lg:text-4xl">CRUNCHBASE</span>
              </div>
              <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-3xl px-12 py-8 lg:px-16 lg:py-10 border border-purple-400/30 shadow-xl">
                <span className="text-white font-bold text-3xl lg:text-4xl">DAN</span>
              </div>
              <div className="bg-gradient-to-r from-pink-500/20 to-red-500/20 backdrop-blur-md rounded-3xl px-12 py-8 lg:px-16 lg:py-10 border border-pink-400/30 shadow-xl">
                <span className="text-white font-bold text-3xl lg:text-4xl">THE MANIFEST</span>
              </div>
              <div className="bg-gradient-to-r from-green-500/20 to-cyan-500/20 backdrop-blur-md rounded-3xl px-12 py-8 lg:px-16 lg:py-10 border border-green-400/30 shadow-xl">
                <span className="text-white font-bold text-3xl lg:text-4xl">CLUTCH</span>
              </div>
            </div>
          </motion.div>

          {/* MASSIVE Stats - Even Larger */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-16 lg:gap-20 max-w-none mx-auto"
          >
            <div className="text-center bg-gradient-to-br from-cyan-500/10 to-blue-500/10 backdrop-blur-md rounded-3xl p-10 lg:p-12 border border-cyan-400/20 shadow-2xl">
              <div className="text-7xl md:text-8xl lg:text-9xl xl:text-10xl font-black bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-6 drop-shadow-lg">500+</div>
              <div className="text-gray-200 font-bold text-xl lg:text-2xl xl:text-3xl">PROJECTS DELIVERED</div>
            </div>
            <div className="text-center bg-gradient-to-br from-purple-500/10 to-pink-500/10 backdrop-blur-md rounded-3xl p-10 lg:p-12 border border-purple-400/20 shadow-2xl">
              <div className="text-7xl md:text-8xl lg:text-9xl xl:text-10xl font-black bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent mb-6 drop-shadow-lg">50+</div>
              <div className="text-gray-200 font-bold text-xl lg:text-2xl xl:text-3xl">HAPPY CLIENTS</div>
            </div>
            <div className="text-center bg-gradient-to-br from-pink-500/10 to-red-500/10 backdrop-blur-md rounded-3xl p-10 lg:p-12 border border-pink-400/20 shadow-2xl">
              <div className="text-7xl md:text-8xl lg:text-9xl xl:text-10xl font-black bg-gradient-to-r from-pink-400 to-red-500 bg-clip-text text-transparent mb-6 drop-shadow-lg">5+</div>
              <div className="text-gray-200 font-bold text-xl lg:text-2xl xl:text-3xl">YEARS EXPERIENCE</div>
            </div>
            <div className="text-center bg-gradient-to-br from-yellow-500/10 to-orange-500/10 backdrop-blur-md rounded-3xl p-10 lg:p-12 border border-yellow-400/20 shadow-2xl">
              <div className="text-7xl md:text-8xl lg:text-9xl xl:text-10xl font-black bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-6 drop-shadow-lg">24/7</div>
              <div className="text-gray-200 font-bold text-xl lg:text-2xl xl:text-3xl">SUPPORT</div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.4 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-12 border-2 border-white/30 rounded-full flex justify-center backdrop-blur-sm">
          <div className="w-1 h-4 bg-white/60 rounded-full mt-2 animate-bounce"></div>
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
