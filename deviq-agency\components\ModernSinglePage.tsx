'use client';

import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import {
  FloatingParticles,
  MorphingBackground,
  ParallaxSection,
  InteractiveBlob,
  AnimatedText,
  LiquidButton,
  GlitchText,
  useScrollAnimation
} from './AdvancedAnimations';
import {
  MovingShapes,
  CursorFollower,
  AnimatedGrid,
  FloatingTechIcons,
  MorphingBlob,
  ParticleSystem,
  HolographicCard,
  GlowingOrb
} from './MovingGraphics';
import { 
  SparklesIcon,
  ArrowRightIcon,
  PlayIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  StarIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarDaysIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const ModernSinglePage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const services = [
    {
      icon: CodeBracketIcon,
      title: 'Custom Web Development',
      description: 'Modern, responsive websites and web applications built with cutting-edge technologies for optimal performance and user experience.',
      features: ['React/Next.js', 'Node.js', 'TypeScript', 'Progressive Web Apps', 'E-commerce Solutions', 'CMS Development', 'API Integration', 'Database Design'],
      image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R15,000',
      timeline: '2-8 weeks',
      category: 'Web Development',
      technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS']
    },
    {
      icon: DevicePhoneMobileIcon,
      title: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications for iOS and Android with seamless user experiences and modern UI/UX design.',
      features: ['React Native', 'Flutter', 'iOS/Android Native', 'App Store Optimization', 'Push Notifications', 'Offline Functionality', 'Biometric Auth', 'Real-time Sync'],
      image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R25,000',
      timeline: '4-12 weeks',
      category: 'Mobile Development',
      technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin']
    },
    {
      icon: CloudIcon,
      title: 'Cloud & DevOps Solutions',
      description: 'Scalable cloud infrastructure, deployment automation, and DevOps solutions for enterprise-grade applications with 99.9% uptime.',
      features: ['AWS/Azure/GCP', 'Docker/Kubernetes', 'CI/CD Pipelines', 'Monitoring & Analytics', 'Auto-scaling', 'Security', 'Load Balancing', 'Disaster Recovery'],
      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R20,000',
      timeline: '3-10 weeks',
      category: 'Cloud Solutions',
      technologies: ['AWS', 'Docker', 'Kubernetes', 'Terraform']
    },
    {
      icon: CpuChipIcon,
      title: 'AI/ML Solutions',
      description: 'Intelligent systems powered by artificial intelligence and machine learning for data-driven business insights and automation.',
      features: ['Natural Language Processing', 'Computer Vision', 'Predictive Analytics', 'Chatbots', 'Data Mining', 'Deep Learning', 'Neural Networks', 'Model Training'],
      image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R30,000',
      timeline: '6-16 weeks',
      category: 'AI/ML',
      technologies: ['Python', 'TensorFlow', 'PyTorch', 'OpenAI']
    },
    {
      icon: ShieldCheckIcon,
      title: 'Enterprise Software',
      description: 'Custom enterprise solutions including ERP, CRM, and workflow automation systems for large organizations with advanced security.',
      features: ['ERP Systems', 'CRM Solutions', 'Workflow Automation', 'Data Analytics', 'Integration APIs', 'Security Compliance', 'User Management', 'Reporting'],
      image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R50,000',
      timeline: '8-24 weeks',
      category: 'Enterprise',
      technologies: ['Java', 'Spring', 'PostgreSQL', 'Redis']
    },
    {
      icon: RocketLaunchIcon,
      title: 'Digital Transformation',
      description: 'Complete digital transformation services to modernize your business processes and technology infrastructure for the digital age.',
      features: ['Process Automation', 'Legacy System Migration', 'Digital Strategy', 'Change Management', 'Training & Support', 'ROI Analysis', 'Performance Optimization', 'Scalability Planning'],
      image: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      price: 'From R75,000',
      timeline: '12-36 weeks',
      category: 'Transformation',
      technologies: ['Microservices', 'API Gateway', 'Event Streaming', 'Analytics']
    }
  ];

  const stats = [
    { number: '500+', label: 'Projects Delivered' },
    { number: '50+', label: 'Happy Clients' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support' }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      position: 'CEO',
      company: 'TechStore Inc.',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      text: 'AMPD Dev-IQ transformed our online presence completely. Their e-commerce platform increased our sales by 300% within 6 months. The attention to detail and technical expertise is unmatched.',
      rating: 5,
      project: 'E-Commerce Platform'
    },
    {
      name: 'Dr. Michael Chen',
      position: 'Chief Medical Officer',
      company: 'HealthCare Plus',
      image: 'https://images.unsplash.com/photo-*************-e413f6a5b16d?w=150&h=150&fit=crop&crop=face',
      text: 'The healthcare management system has revolutionized how we manage patient care. The system is intuitive, secure, and has significantly improved our operational efficiency.',
      rating: 5,
      project: 'Healthcare Management System'
    },
    {
      name: 'Jennifer Williams',
      position: 'Digital Banking Director',
      company: 'SecureBank',
      image: 'https://images.unsplash.com/photo-*************-15a19d654956?w=150&h=150&fit=crop&crop=face',
      text: 'Our customers love the new mobile banking app. It\'s intuitive, secure, and feature-rich. AMPD Dev-IQ delivered exactly what we needed to compete in the digital banking space.',
      rating: 5,
      project: 'Mobile Banking App'
    },
    {
      name: 'David Rodriguez',
      position: 'Operations Manager',
      company: 'LogiFlow Corp',
      image: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      text: 'The logistics management system streamlined our entire operation. Real-time tracking and automated workflows have reduced our operational costs by 40%.',
      rating: 5,
      project: 'Logistics Management System'
    }
  ];

  const portfolio = [
    {
      id: 1,
      title: 'Advanced E-Commerce Platform',
      category: 'Web Development',
      description: 'A comprehensive e-commerce platform with AI-powered recommendations, real-time inventory management, and advanced analytics dashboard.',
      image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['React', 'Next.js', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe'],
      impact: '300% increase in sales, 50% reduction in cart abandonment, 2.5M+ users',
      client: 'TechStore Inc.',
      duration: '6 months',
      features: ['AI Recommendations', 'Real-time Inventory', 'Multi-payment Gateway', 'Admin Dashboard']
    },
    {
      id: 2,
      title: 'Healthcare Management System',
      category: 'Enterprise Software',
      description: 'Comprehensive healthcare management system with patient records, appointment scheduling, telemedicine, and HIPAA compliance.',
      image: 'https://images.unsplash.com/photo-*************-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['React', 'Python', 'Django', 'MongoDB', 'Docker', 'WebRTC'],
      impact: '60% reduction in administrative time, improved patient satisfaction by 85%',
      client: 'HealthCare Plus',
      duration: '8 months',
      features: ['Patient Records', 'Telemedicine', 'Appointment Scheduling', 'Billing System']
    },
    {
      id: 3,
      title: 'Mobile Banking Application',
      category: 'Mobile Development',
      description: 'Secure mobile banking application with biometric authentication, real-time transactions, and advanced security features.',
      image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['React Native', 'Node.js', 'PostgreSQL', 'AWS', 'Biometric SDK'],
      impact: '2M+ downloads, 4.8 star rating, 95% user retention, 99.9% uptime',
      client: 'SecureBank',
      duration: '10 months',
      features: ['Biometric Auth', 'Real-time Transactions', 'Investment Tracking', 'Budget Planning']
    },
    {
      id: 4,
      title: 'AI-Powered Analytics Dashboard',
      category: 'AI/ML',
      description: 'Advanced analytics dashboard with machine learning insights, predictive analytics, and real-time data visualization.',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['Python', 'TensorFlow', 'React', 'D3.js', 'Apache Kafka', 'Redis'],
      impact: '40% improvement in decision making, 60% faster insights generation',
      client: 'DataCorp Analytics',
      duration: '5 months',
      features: ['Predictive Analytics', 'Real-time Dashboards', 'ML Models', 'Data Visualization']
    },
    {
      id: 5,
      title: 'Enterprise Resource Planning System',
      category: 'Enterprise Software',
      description: 'Complete ERP solution with inventory management, HR, accounting, and supply chain optimization for manufacturing companies.',
      image: 'https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['Java', 'Spring Boot', 'Angular', 'PostgreSQL', 'Docker', 'Kubernetes'],
      impact: '35% reduction in operational costs, 50% improvement in efficiency',
      client: 'Manufacturing Solutions Ltd',
      duration: '12 months',
      features: ['Inventory Management', 'HR Module', 'Financial Reporting', 'Supply Chain']
    },
    {
      id: 6,
      title: 'Cloud Infrastructure Migration',
      category: 'Cloud Solutions',
      description: 'Complete cloud migration from on-premise to AWS with microservices architecture, auto-scaling, and disaster recovery.',
      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=80',
      technologies: ['AWS', 'Docker', 'Kubernetes', 'Terraform', 'Jenkins', 'Monitoring'],
      impact: '70% reduction in infrastructure costs, 99.9% uptime achieved',
      client: 'Global Enterprises',
      duration: '4 months',
      features: ['Auto-scaling', 'Load Balancing', 'Disaster Recovery', 'Monitoring']
    }
  ];

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const message = `Hi! I'm ${formData.get('name')} from ${formData.get('company')}.

Email: ${formData.get('email')}
Budget: ${formData.get('budget')}

Project Details:
${formData.get('message')}

I'd like to discuss this project with you.`;

    const whatsappUrl = `https://wa.me/***********?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="min-h-screen w-full relative overflow-hidden">
      {/* Advanced Animation Components */}
      <FloatingParticles />
      <MorphingBackground />
      <MovingShapes />
      <CursorFollower />
      <AnimatedGrid />
      <FloatingTechIcons />
      <ParticleSystem />

      {/* Glowing Orbs */}
      <GlowingOrb size={150} color="#22d3ee" className="top-20 right-20" />
      <GlowingOrb size={100} color="#a855f7" className="bottom-40 left-20" />
      <GlowingOrb size={80} color="#f97316" className="top-1/2 left-1/3" />

      {/* Professional Moving Background System */}
      <div className="fixed inset-0 z-0">
        {/* Primary Moving GIF Background - Professional Software Development */}
        <div className="absolute inset-0">
          <img
            src="https://media.giphy.com/media/qgQUggAC3Pfv687qPC/giphy.gif"
            alt="Software Development Animation"
            className="w-full h-full object-cover opacity-20"
            style={{
              filter: 'hue-rotate(200deg) saturate(0.8)',
              animation: 'infinite-loop 20s linear infinite'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900/95 via-purple-900/90 to-slate-900/95"></div>
        </div>

        {/* Secondary Professional Coding GIF */}
        <div className="absolute inset-0 opacity-15">
          <img
            src="https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif"
            alt="Professional Coding Animation"
            className="w-full h-full object-cover mix-blend-overlay"
            style={{
              filter: 'brightness(0.7) contrast(1.2)',
              animation: 'infinite-loop 15s linear infinite reverse'
            }}
          />
        </div>

        {/* Tertiary Data Flow Animation */}
        <div className="absolute inset-0 opacity-10">
          <img
            src="https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif"
            alt="Data Flow Animation"
            className="w-full h-full object-cover mix-blend-screen"
            style={{
              filter: 'hue-rotate(120deg) brightness(0.8)',
              animation: 'infinite-loop 25s linear infinite'
            }}
          />
        </div>

        {/* Enhanced Geometric Elements with Neon Colors */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob"></div>
          <div className="absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
        </div>

        {/* Enhanced Grid Overlay with Neon Effect */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse"></div>

        {/* Particle Effect Overlay */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
          <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000"></div>
          <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000"></div>
        </div>
      </div>

      {/* Content - Full Width Professional Layout */}
      <div className="relative z-10 w-full">
        {/* Modern Header */}
        <motion.header
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6 }}
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
            isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'
          }`}
        >
          <nav className="professional-container py-6">
            <div className="flex items-center justify-between">
              {/* Logo - Larger & Better Spaced */}
              <div className="flex items-center space-x-6">
                <div className="w-20 h-20 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-3xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40">
                  <img src="/ampd-logo.png" alt="AMPD Dev-IQ" className="w-14 h-14 object-contain" />
                </div>
                <div>
                  <div className="text-3xl md:text-4xl font-black text-white">AMPD Dev-IQ</div>
                  <div className="text-lg text-cyan-400">Smart Software. Limitless Potential.</div>
                </div>
              </div>

              {/* Navigation - Larger Text */}
              <div className="hidden lg:flex items-center space-x-12">
                <a href="#services" className="text-white/80 hover:text-white transition-colors text-xl font-semibold">Services</a>
                <a href="#about" className="text-white/80 hover:text-white transition-colors text-xl font-semibold">About</a>
                <a href="#portfolio" className="text-white/80 hover:text-white transition-colors text-xl font-semibold">Portfolio</a>
                <a href="#contact" className="text-white/80 hover:text-white transition-colors text-xl font-semibold">Contact</a>
              </div>

              {/* CTA Button - Larger */}
              <div className="hidden lg:block">
                <a
                  href="#contact"
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold text-xl px-8 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-xl"
                >
                  Get Started
                </a>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors"
              >
                {isMenuOpen ? (
                  <XMarkIcon className="w-6 h-6 text-white" />
                ) : (
                  <Bars3Icon className="w-6 h-6 text-white" />
                )}
              </button>
            </div>

            {/* Mobile Menu */}
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="lg:hidden mt-4 py-4 border-t border-white/20"
              >
                <div className="flex flex-col space-y-4">
                  <a href="#services" className="text-white/80 hover:text-white transition-colors py-2">Services</a>
                  <a href="#about" className="text-white/80 hover:text-white transition-colors py-2">About</a>
                  <a href="#portfolio" className="text-white/80 hover:text-white transition-colors py-2">Portfolio</a>
                  <a href="#contact" className="text-white/80 hover:text-white transition-colors py-2">Contact</a>
                  <a
                    href="#contact"
                    className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4"
                  >
                    Get Started
                  </a>
                </div>
              </motion.div>
            )}
          </nav>
        </motion.header>

        {/* Hero Section - Full Width & Properly Sized */}
        <section className="min-h-screen w-full flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-20">
          <div className="w-full max-w-none text-center">
            {/* Enhanced Badge with Neon Glow */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="inline-flex items-center space-x-4 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-12 py-6 mb-16 shadow-2xl shadow-cyan-500/20"
            >
              <SparklesIcon className="w-8 h-8 text-cyan-400 animate-pulse" />
              <span className="text-white font-bold text-xl md:text-2xl neon-glow">Trusted by 500+ Companies Worldwide</span>
            </motion.div>

            {/* Enhanced Main Headline with Advanced Animations - Larger & Full Width */}
            <div className="relative w-full">
              <InteractiveBlob className="absolute inset-0 -z-10" />
              <AnimatedText
                text="SMART SOFTWARE."
                className="block text-7xl sm:text-8xl md:text-9xl lg:text-10xl xl:text-11xl 2xl:text-12xl font-black font-poppins mb-6 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent"
                delay={0.2}
              />
              <GlitchText
                text="LIMITLESS"
                className="block text-7xl sm:text-8xl md:text-9xl lg:text-10xl xl:text-11xl 2xl:text-12xl font-black font-poppins mb-6 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent"
              />
              <AnimatedText
                text="POTENTIAL."
                className="block text-7xl sm:text-8xl md:text-9xl lg:text-10xl xl:text-11xl 2xl:text-12xl font-black font-poppins mb-16 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent"
                delay={0.6}
              />
            </div>

            {/* Subtitle - Larger & Full Width */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-gray-200 mb-16 max-w-none mx-auto leading-relaxed px-4"
            >
              We transform your <span className="text-cyan-400 font-semibold">business ideas</span> into
              <span className="text-purple-400 font-semibold"> powerful digital solutions</span> that drive growth and innovation.
            </motion.p>

            {/* Enhanced CTA Buttons with Liquid Animation - Larger & Better Spaced */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col lg:flex-row gap-8 justify-center items-center mb-20"
            >
              <LiquidButton
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-2xl md:text-3xl px-16 py-8 shadow-2xl"
              >
                <div className="flex items-center space-x-4">
                  <span>Start Your Project</span>
                  <ArrowRightIcon className="w-8 h-8" />
                </div>
              </LiquidButton>

              <motion.a
                href="#about"
                className="group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-2xl md:text-3xl px-16 py-8 rounded-3xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-4"
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 0 40px rgba(34, 211, 238, 0.6)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <PlayIcon className="w-6 h-6 ml-0.5" />
                </motion.div>
                <span>Watch Demo</span>
              </motion.a>
            </motion.div>

            {/* Stats - Larger & Full Width */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-2 lg:grid-cols-4 gap-12 w-full max-w-none px-8"
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center professional-card">
                  <div className="text-6xl md:text-7xl lg:text-8xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-4 neon-glow">
                    {stat.number}
                  </div>
                  <div className="text-gray-300 font-semibold text-xl md:text-2xl">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Professional Services Section */}
        <ParallaxSection speed={0.3}>
          <section id="services" className="professional-section relative">
            {/* Professional Moving Background */}
            <div className="absolute inset-0 opacity-8">
              <img
                src="https://media.giphy.com/media/LaVp0AyqR5bGsC5Cbm/giphy.gif"
                alt="Professional Team Collaboration"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(0.4) contrast(1.1)',
                  animation: 'infinite-loop 30s linear infinite'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-purple-900/85 to-cyan-900/85"></div>
            </div>

            <div className="w-full max-w-none mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <h2 className="professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow">
                  OUR SERVICES
                </h2>
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8">
                  Comprehensive digital solutions to transform your business with cutting-edge technology
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full"></div>
              </motion.div>

            <div className="professional-grid">
              {services.map((service, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="professional-card group"
                >
                  {/* Professional Service Image */}
                  <div className="professional-image-overlay h-56 mb-6">
                    <img
                      src={service.image}
                      alt={service.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent"></div>

                    {/* Category Badge */}
                    <div className="absolute top-4 left-4">
                      <span className="text-xs font-bold bg-cyan-500/90 text-white px-3 py-1 rounded-full backdrop-blur-sm">
                        {service.category}
                      </span>
                    </div>

                    {/* Price and Timeline */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between text-white">
                        <span className="text-sm font-bold bg-gradient-to-r from-cyan-500/80 to-purple-600/80 px-3 py-2 rounded-lg backdrop-blur-sm">
                          {service.price}
                        </span>
                        <span className="text-sm font-semibold bg-slate-900/80 px-3 py-2 rounded-lg backdrop-blur-sm">
                          {service.timeline}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Service Header */}
                  <div className="flex items-start mb-6">
                    <div className="w-14 h-14 bg-gradient-to-br from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl">
                      <service.icon className="w-7 h-7 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                        {service.title}
                      </h3>
                      <div className="flex flex-wrap gap-1">
                        {service.technologies.slice(0, 3).map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-300 mb-6 leading-relaxed text-sm group-hover:text-gray-200 transition-colors duration-300">
                    {service.description}
                  </p>

                  {/* Key Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-cyan-400 mb-3">Key Features:</h4>
                    <ul className="grid grid-cols-2 gap-2">
                      {service.features.slice(0, 6).map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                          <CheckCircleIcon className="w-4 h-4 text-cyan-400 mr-2 flex-shrink-0" />
                          <span className="text-xs">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Professional CTA Button */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="professional-button w-full text-sm font-bold py-3"
                    onClick={() => {
                      const message = `Hi! I'm interested in ${service.title} (${service.price}). Can you provide more details about this service?`;
                      window.open(`https://wa.me/***********?text=${encodeURIComponent(message)}`, '_blank');
                    }}
                  >
                    Get Quote & Consultation
                  </motion.button>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        </ParallaxSection>

        {/* Professional Portfolio Section */}
        <ParallaxSection speed={0.4}>
          <section id="portfolio" className="professional-section relative">
            {/* Professional Moving Background */}
            <div className="absolute inset-0 opacity-6">
              <img
                src="https://media.giphy.com/media/xT9IgzoKnwFNmISR8I/giphy.gif"
                alt="Professional Development Background"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(0.3) contrast(1.2) hue-rotate(200deg)',
                  animation: 'infinite-loop 35s linear infinite reverse'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/85 to-purple-900/85"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <h2 className="professional-heading bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow">
                  OUR PORTFOLIO
                </h2>
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8">
                  Showcasing our successful projects and the measurable impact we've delivered for our clients
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-cyan-400 to-purple-500 mx-auto rounded-full"></div>
              </motion.div>

              <div className="professional-grid">
                {portfolio.map((project, index) => (
                  <motion.div
                    key={project.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="professional-card group"
                  >
                    {/* Professional Project Image */}
                    <div className="professional-image-overlay h-52 mb-6">
                      <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent"></div>

                      {/* Category and Duration */}
                      <div className="absolute top-4 left-4 right-4 flex justify-between">
                        <span className="text-xs font-bold bg-cyan-500/90 text-white px-3 py-1 rounded-full backdrop-blur-sm">
                          {project.category}
                        </span>
                        <span className="text-xs font-semibold bg-slate-900/80 text-white px-3 py-1 rounded-full backdrop-blur-sm">
                          {project.duration}
                        </span>
                      </div>

                      {/* Client */}
                      <div className="absolute bottom-4 left-4">
                        <span className="text-sm font-bold text-white bg-gradient-to-r from-purple-500/80 to-pink-600/80 px-3 py-2 rounded-lg backdrop-blur-sm">
                          {project.client}
                        </span>
                      </div>
                    </div>

                    {/* Project Header */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-cyan-400 transition-colors duration-300">
                        {project.title}
                      </h3>
                      <p className="text-gray-300 text-sm leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                        {project.description}
                      </p>
                    </div>

                    {/* Key Features */}
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-cyan-400 mb-2">Key Features:</h4>
                      <div className="grid grid-cols-2 gap-1">
                        {project.features.map((feature, featureIndex) => (
                          <span
                            key={featureIndex}
                            className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-md"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Technologies */}
                    <div className="mb-6">
                      <h4 className="text-sm font-semibold text-orange-400 mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.map((tech, techIndex) => (
                          <span
                            key={techIndex}
                            className="text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded-md"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Impact Results */}
                    <div className="border-t border-white/10 pt-4 mb-6">
                      <h4 className="text-sm font-semibold text-green-400 mb-2">Measurable Impact:</h4>
                      <p className="text-sm text-gray-300 leading-relaxed">{project.impact}</p>
                    </div>

                    {/* Professional CTA */}
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="professional-button w-full text-sm font-bold py-3"
                      onClick={() => {
                        const message = `Hi! I'm interested in a similar project to ${project.title}. Can we discuss my requirements?`;
                        window.open(`https://wa.me/***********?text=${encodeURIComponent(message)}`, '_blank');
                      }}
                    >
                      Discuss Similar Project
                    </motion.button>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        </ParallaxSection>

        {/* Professional About Section */}
        <section id="about" className="professional-section relative">
          {/* Professional Moving Background */}
          <div className="absolute inset-0 opacity-8">
            <img
              src="https://media.giphy.com/media/l46Cy1rHbQ92uuLXa/giphy.gif"
              alt="Professional Team Working"
              className="w-full h-full object-cover"
              style={{
                filter: 'brightness(0.4) contrast(1.1) hue-rotate(300deg)',
                animation: 'infinite-loop 40s linear infinite'
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/85 to-orange-900/85"></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-content"
              >
                <h2 className="professional-heading bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow mb-8">
                  ABOUT AMPD DEV-IQ
                </h2>
                <div className="w-24 h-1 bg-gradient-to-r from-purple-400 to-orange-500 mb-8 rounded-full"></div>

                <p className="text-xl md:text-2xl text-gray-200 mb-8 leading-relaxed">
                  At <span className="text-cyan-400 font-bold">AMPD Dev-IQ</span>, we are a Cape Town-based software development powerhouse
                  specializing in transforming businesses through intelligent technology solutions.
                </p>

                <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                  With over 5 years of experience and 500+ successful projects, we combine cutting-edge technology
                  with deep industry expertise to deliver solutions that drive real business growth. Our team of
                  expert developers, designers, and strategists work collaboratively to turn your vision into reality.
                </p>

                {/* Key Differentiators */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-white mb-4">Why Choose Us:</h3>
                  <ul className="space-y-3">
                    {[
                      'Proven track record with 500+ successful projects',
                      'Expert team with 5+ years of industry experience',
                      'Cutting-edge technology stack and best practices',
                      '24/7 support and maintenance services',
                      'Transparent pricing and project management',
                      'Based in Cape Town, serving clients globally'
                    ].map((point, index) => (
                      <li key={index} className="flex items-center text-gray-300">
                        <CheckCircleIcon className="w-5 h-5 text-cyan-400 mr-3" />
                        <span>{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Professional Stats */}
                <div className="grid grid-cols-2 gap-6">
                  <div className="professional-card text-center p-6">
                    <div className="text-4xl lg:text-5xl font-black text-cyan-400 mb-2 neon-glow">98%</div>
                    <div className="text-lg text-gray-300 font-semibold">Client Satisfaction</div>
                    <div className="text-sm text-gray-400">Based on 50+ reviews</div>
                  </div>
                  <div className="professional-card text-center p-6">
                    <div className="text-4xl lg:text-5xl font-black text-purple-400 mb-2 neon-glow">100%</div>
                    <div className="text-lg text-gray-300 font-semibold">On-Time Delivery</div>
                    <div className="text-sm text-gray-400">500+ projects delivered</div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="grid grid-cols-2 gap-6"
              >
                {[
                  { icon: RocketLaunchIcon, title: 'Innovation', desc: 'Cutting-edge solutions' },
                  { icon: ShieldCheckIcon, title: 'Quality', desc: 'Premium standards' },
                  { icon: CheckCircleIcon, title: 'Reliability', desc: 'Trusted delivery' },
                  { icon: StarIcon, title: 'Excellence', desc: 'Outstanding results' }
                ].map((item, index) => (
                  <div key={index} className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-white mb-2">{item.title}</h4>
                    <p className="text-gray-400 text-sm">{item.desc}</p>
                  </div>
                ))}
              </motion.div>
            </div>
          </div>
        </section>

        {/* Professional Technologies Section */}
        <ParallaxSection speed={0.2}>
          <section className="professional-section relative">
            {/* Professional Moving Background */}
            <div className="absolute inset-0 opacity-6">
              <img
                src="https://media.giphy.com/media/qgQUggAC3Pfv687qPC/giphy.gif"
                alt="Technology Background"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(0.3) contrast(1.2) hue-rotate(120deg)',
                  animation: 'infinite-loop 45s linear infinite reverse'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-green-900/85 to-cyan-900/85"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <h2 className="professional-heading bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow">
                  TECHNOLOGIES WE MASTER
                </h2>
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8">
                  Cutting-edge technology stack powering next-generation solutions
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-green-400 to-cyan-500 mx-auto rounded-full"></div>
              </motion.div>

              <div className="professional-grid lg:grid-cols-4">
                {[
                  {
                    category: 'Frontend',
                    icon: '🎨',
                    technologies: ['React', 'Next.js', 'Vue.js', 'Angular', 'TypeScript', 'Tailwind CSS'],
                    color: 'from-blue-500 to-cyan-600',
                    image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Backend',
                    icon: '⚙️',
                    technologies: ['Node.js', 'Python', 'Java', 'C#', 'PHP', 'Go'],
                    color: 'from-green-500 to-emerald-600',
                    image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Mobile',
                    icon: '📱',
                    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Xamarin', 'Ionic'],
                    color: 'from-purple-500 to-pink-600',
                    image: 'https://images.unsplash.com/photo-**********-87deedd944c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Cloud & DevOps',
                    icon: '☁️',
                    technologies: ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Terraform'],
                    color: 'from-orange-500 to-red-600',
                    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Database',
                    icon: '🗄️',
                    technologies: ['PostgreSQL', 'MongoDB', 'MySQL', 'Redis', 'Elasticsearch', 'DynamoDB'],
                    color: 'from-indigo-500 to-purple-600',
                    image: 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'AI/ML',
                    icon: '🤖',
                    technologies: ['TensorFlow', 'PyTorch', 'OpenAI', 'Scikit-learn', 'Pandas', 'NumPy'],
                    color: 'from-pink-500 to-rose-600',
                    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Testing',
                    icon: '🧪',
                    technologies: ['Jest', 'Cypress', 'Selenium', 'Pytest', 'JUnit', 'Postman'],
                    color: 'from-teal-500 to-cyan-600',
                    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  },
                  {
                    category: 'Tools & Platforms',
                    icon: '🛠️',
                    technologies: ['Git', 'Jenkins', 'Jira', 'Figma', 'VS Code', 'Slack'],
                    color: 'from-yellow-500 to-orange-600',
                    image: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300&q=80'
                  }
                ].map((tech, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="professional-card group"
                  >
                    {/* Technology Image */}
                    <div className="professional-image-overlay h-40 mb-6">
                      <img
                        src={tech.image}
                        alt={tech.category}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/20 to-transparent"></div>

                      {/* Icon Overlay */}
                      <div className="absolute top-4 right-4">
                        <div className={`w-12 h-12 bg-gradient-to-r ${tech.color} rounded-xl flex items-center justify-center shadow-xl`}>
                          <span className="text-xl">{tech.icon}</span>
                        </div>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300">
                      {tech.category}
                    </h3>

                    <div className="grid grid-cols-2 gap-2">
                      {tech.technologies.map((technology, techIndex) => (
                        <div
                          key={techIndex}
                          className="text-xs bg-white/10 text-gray-300 px-2 py-1 rounded-md hover:bg-white/20 transition-colors duration-300 text-center"
                        >
                          {technology}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Technology CTA */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-16"
              >
                <p className="text-gray-300 text-lg mb-6">
                  Don't see your preferred technology? We're always learning and adapting to new tools.
                </p>
                <LiquidButton
                  onClick={() => {
                    const message = "Hi! I'd like to discuss my project's technology requirements with your team.";
                    window.open(`https://wa.me/***********?text=${encodeURIComponent(message)}`, '_blank');
                  }}
                  className="text-xl px-12 py-6 shadow-2xl"
                >
                  Discuss Technology Stack
                </LiquidButton>
              </motion.div>
            </div>
          </section>
        </ParallaxSection>

        {/* Professional Testimonials Section */}
        <ParallaxSection speed={0.2}>
          <section className="professional-section relative">
            {/* Professional Moving Background */}
            <div className="absolute inset-0 opacity-6">
              <img
                src="https://media.giphy.com/media/LaVp0AyqR5bGsC5Cbm/giphy.gif"
                alt="Professional Testimonials Background"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(0.4) contrast(1.1) hue-rotate(180deg)',
                  animation: 'infinite-loop 55s linear infinite reverse'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-green-900/85 to-cyan-900/85"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <h2 className="professional-heading bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent neon-glow">
                  CLIENT TESTIMONIALS
                </h2>
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8">
                  Hear what our satisfied clients say about our work and measurable results
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-green-400 to-cyan-500 mx-auto rounded-full"></div>
              </motion.div>

              <div className="professional-grid lg:grid-cols-2">
                {testimonials.map((testimonial, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="professional-card group"
                  >
                    {/* Rating Stars */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <StarIcon key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <span className="text-xs bg-green-500/20 text-green-300 px-3 py-1 rounded-full">
                        Verified Client
                      </span>
                    </div>

                    {/* Testimonial Text */}
                    <blockquote className="text-gray-200 text-lg mb-8 italic leading-relaxed group-hover:text-gray-100 transition-colors duration-300">
                      "{testimonial.text}"
                    </blockquote>

                    {/* Client Info */}
                    <div className="flex items-center mb-6">
                      <div className="professional-image-overlay w-20 h-20 rounded-full mr-4 flex-shrink-0">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <div className="font-bold text-white text-xl mb-1 group-hover:text-cyan-400 transition-colors duration-300">
                          {testimonial.name}
                        </div>
                        <div className="text-cyan-400 text-sm font-semibold">{testimonial.position}</div>
                        <div className="text-gray-400 text-sm">{testimonial.company}</div>
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="border-t border-white/10 pt-6">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-400">Project:</span>
                        <span className="text-sm bg-purple-500/20 text-purple-300 px-3 py-1 rounded-lg font-semibold">
                          {testimonial.project}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        </ParallaxSection>

        {/* Process Section - How We Work */}
        <ParallaxSection speed={0.3}>
          <section className="py-32 px-8 relative">
            {/* Section Background */}
            <div className="absolute inset-0 opacity-5">
              <img
                src="https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                alt="Process Background"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-orange-900/80 to-purple-900/80"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-24"
              >
                <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-purple-500 bg-clip-text text-transparent neon-glow">
                  HOW WE WORK
                </h2>
                <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed">
                  Our proven process ensures successful project delivery every time
                </p>
              </motion.div>

              {/* Process Steps */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[
                  {
                    step: '01',
                    title: 'Discovery & Planning',
                    description: 'We analyze your requirements, understand your business goals, and create a detailed project roadmap.',
                    icon: '🔍',
                    color: 'from-cyan-500 to-blue-600'
                  },
                  {
                    step: '02',
                    title: 'Design & Prototype',
                    description: 'Our team creates wireframes, mockups, and interactive prototypes to visualize your solution.',
                    icon: '🎨',
                    color: 'from-purple-500 to-pink-600'
                  },
                  {
                    step: '03',
                    title: 'Development & Testing',
                    description: 'We build your solution using best practices, with continuous testing and quality assurance.',
                    icon: '⚡',
                    color: 'from-orange-500 to-red-600'
                  },
                  {
                    step: '04',
                    title: 'Launch & Support',
                    description: 'We deploy your solution and provide ongoing support, maintenance, and optimization.',
                    icon: '🚀',
                    color: 'from-green-500 to-cyan-600'
                  }
                ].map((process, index) => (
                  <HolographicCard
                    key={index}
                    className="enhanced-card text-center relative overflow-hidden"
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="p-8"
                    >
                      {/* Step Number */}
                      <div className={`w-20 h-20 bg-gradient-to-r ${process.color} rounded-full flex items-center justify-center mx-auto mb-6 text-white font-black text-2xl shadow-2xl`}>
                        {process.step}
                      </div>

                      {/* Icon */}
                      <div className="text-4xl mb-4">{process.icon}</div>

                      {/* Title */}
                      <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors duration-300">
                        {process.title}
                      </h3>

                      {/* Description */}
                      <p className="text-gray-300 leading-relaxed">
                        {process.description}
                      </p>
                    </motion.div>
                  </HolographicCard>
                ))}
              </div>

              {/* CTA Section */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-16"
              >
                <LiquidButton
                  onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  className="text-xl px-12 py-6 shadow-2xl"
                >
                  Start Your Project Today
                </LiquidButton>
              </motion.div>
            </div>
          </section>
        </ParallaxSection>

        {/* Pricing Section */}
        <ParallaxSection speed={0.2}>
          <section className="py-32 px-8 relative">
            {/* Section Background */}
            <div className="absolute inset-0 opacity-5">
              <img
                src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
                alt="Pricing Background"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-24"
              >
                <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-cyan-500 bg-clip-text text-transparent neon-glow">
                  PRICING PACKAGES
                </h2>
                <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed">
                  Transparent pricing for every business size and requirement
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {[
                  {
                    name: 'Starter',
                    price: 'R15,000',
                    period: 'Starting from',
                    description: 'Perfect for small businesses and startups',
                    features: [
                      'Responsive Website',
                      'Up to 5 Pages',
                      'Contact Forms',
                      'Basic SEO',
                      '3 Months Support',
                      'Mobile Optimized'
                    ],
                    popular: false,
                    color: 'from-blue-500 to-cyan-600'
                  },
                  {
                    name: 'Professional',
                    price: 'R35,000',
                    period: 'Starting from',
                    description: 'Ideal for growing businesses',
                    features: [
                      'Custom Web Application',
                      'Database Integration',
                      'User Authentication',
                      'Admin Dashboard',
                      'API Integration',
                      '6 Months Support',
                      'Advanced SEO',
                      'Analytics Setup'
                    ],
                    popular: true,
                    color: 'from-purple-500 to-pink-600'
                  },
                  {
                    name: 'Enterprise',
                    price: 'R75,000',
                    period: 'Starting from',
                    description: 'For large organizations and complex projects',
                    features: [
                      'Full-Stack Solution',
                      'Cloud Infrastructure',
                      'AI/ML Integration',
                      'Advanced Security',
                      'Scalable Architecture',
                      '12 Months Support',
                      'DevOps Setup',
                      'Training Included'
                    ],
                    popular: false,
                    color: 'from-orange-500 to-red-600'
                  }
                ].map((plan, index) => (
                  <HolographicCard
                    key={index}
                    className={`enhanced-card relative overflow-hidden ${plan.popular ? 'border-2 border-cyan-400/50 scale-105' : ''}`}
                  >
                    <motion.div
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="p-8 text-center"
                    >
                      {plan.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <span className="bg-gradient-to-r from-cyan-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold">
                            Most Popular
                          </span>
                        </div>
                      )}

                      <h3 className="text-3xl font-bold text-white mb-4">{plan.name}</h3>

                      <div className="mb-6">
                        <div className={`text-5xl font-black bg-gradient-to-r ${plan.color} bg-clip-text text-transparent mb-2`}>
                          {plan.price}
                        </div>
                        <div className="text-gray-400 text-sm">{plan.period}</div>
                      </div>

                      <p className="text-gray-300 mb-8">{plan.description}</p>

                      <ul className="space-y-3 mb-8 text-left">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center text-gray-300">
                            <CheckCircleIcon className="w-5 h-5 text-cyan-400 mr-3" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <LiquidButton
                        onClick={() => {
                          const message = `Hi! I'm interested in the ${plan.name} package (${plan.price}). Can you provide more details?`;
                          window.open(`https://wa.me/***********?text=${encodeURIComponent(message)}`, '_blank');
                        }}
                        className="w-full text-lg py-4"
                      >
                        Get Started
                      </LiquidButton>
                    </motion.div>
                  </HolographicCard>
                ))}
              </div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-16"
              >
                <p className="text-gray-300 text-lg mb-6">
                  Need a custom solution? We offer flexible pricing for unique requirements.
                </p>
                <LiquidButton
                  onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                  className="text-xl px-12 py-6 shadow-2xl"
                >
                  Request Custom Quote
                </LiquidButton>
              </motion.div>
            </div>
          </section>
        </ParallaxSection>

        {/* Professional FAQ Section */}
        <ParallaxSection speed={0.1}>
          <section className="professional-section relative">
            {/* Professional Moving Background */}
            <div className="absolute inset-0 opacity-6">
              <img
                src="https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif"
                alt="FAQ Background"
                className="w-full h-full object-cover"
                style={{
                  filter: 'brightness(0.3) contrast(1.1) hue-rotate(240deg)',
                  animation: 'infinite-loop 50s linear infinite'
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/85 to-purple-900/85"></div>
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <h2 className="professional-heading bg-gradient-to-r from-indigo-400 to-purple-500 bg-clip-text text-transparent neon-glow">
                  FREQUENTLY ASKED QUESTIONS
                </h2>
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-200 max-w-4xl mx-auto leading-relaxed mb-8">
                  Everything you need to know about working with AMPD Dev-IQ
                </p>
                <div className="w-24 h-1 bg-gradient-to-r from-indigo-400 to-purple-500 mx-auto rounded-full"></div>
              </motion.div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {[
                  {
                    question: "What types of projects do you specialize in?",
                    answer: "We specialize in custom web applications, mobile apps, cloud solutions, AI/ML implementations, enterprise software, and digital transformation projects. Our expertise spans from simple websites to complex enterprise systems."
                  },
                  {
                    question: "How long does a typical project take?",
                    answer: "Project timelines vary based on complexity. Simple websites take 2-8 weeks, mobile apps 4-12 weeks, and enterprise solutions 8-24 weeks. We provide detailed timelines during our initial consultation."
                  },
                  {
                    question: "Do you provide ongoing support and maintenance?",
                    answer: "Yes! We offer comprehensive support packages ranging from 3 months to 12 months, including bug fixes, security updates, performance optimization, and feature enhancements."
                  },
                  {
                    question: "What is your development process?",
                    answer: "We follow a proven 4-step process: Discovery & Planning, Design & Prototype, Development & Testing, and Launch & Support. This ensures quality delivery and client satisfaction."
                  },
                  {
                    question: "Can you work with our existing systems?",
                    answer: "Absolutely! We specialize in system integration and can work with your existing infrastructure, databases, and third-party services to ensure seamless connectivity."
                  },
                  {
                    question: "Do you offer fixed-price or hourly billing?",
                    answer: "We offer both options. For well-defined projects, we provide fixed-price quotes. For ongoing development or projects with evolving requirements, we offer competitive hourly rates."
                  },
                  {
                    question: "What technologies do you use?",
                    answer: "We use modern, industry-standard technologies including React, Node.js, Python, AWS, Docker, and many others. We choose the best tech stack for each project's specific requirements."
                  },
                  {
                    question: "Do you work with international clients?",
                    answer: "Yes! While we're based in Cape Town, we serve clients globally. We're experienced in working across different time zones and have successfully delivered projects worldwide."
                  },
                  {
                    question: "How do you ensure project quality?",
                    answer: "We follow industry best practices including code reviews, automated testing, continuous integration, and quality assurance processes. Every project undergoes thorough testing before delivery."
                  },
                  {
                    question: "Can you help with digital transformation?",
                    answer: "Yes! We offer comprehensive digital transformation services including process automation, legacy system migration, cloud adoption, and strategic technology planning."
                  }
                ].map((faq, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="professional-card group"
                  >
                    <div className="flex items-start mb-4">
                      <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <span className="text-white font-bold text-sm">Q</span>
                      </div>
                      <h3 className="text-lg font-bold text-white group-hover:text-cyan-400 transition-colors duration-300">
                        {faq.question}
                      </h3>
                    </div>
                    <div className="ml-12">
                      <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* FAQ CTA */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-16"
              >
                <p className="text-gray-300 text-lg mb-6">
                  Still have questions? We're here to help!
                </p>
                <LiquidButton
                  onClick={() => {
                    const message = "Hi! I have some questions about your services. Can we schedule a consultation?";
                    window.open(`https://wa.me/***********?text=${encodeURIComponent(message)}`, '_blank');
                  }}
                  className="text-xl px-12 py-6 shadow-2xl"
                >
                  Ask Your Question
                </LiquidButton>
              </motion.div>
            </div>
          </section>
        </ParallaxSection>

        {/* Enhanced Contact Section */}
        <section id="contact" className="py-32 px-8 relative">
          {/* Section Background */}
          <div className="absolute inset-0 opacity-5">
            <img
              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80"
              alt="Contact Background"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80"></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-24"
            >
              <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow">
                GET IN TOUCH
              </h2>
              <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed">
                Ready to transform your business? Let&apos;s discuss your project and unlock your potential.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              {/* Contact Info */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                {[
                  {
                    icon: EnvelopeIcon,
                    title: 'Email Us',
                    details: '<EMAIL>',
                    subtitle: 'Get a detailed response within 24 hours',
                    action: () => window.open('mailto:<EMAIL>', '_blank')
                  },
                  {
                    icon: PhoneIcon,
                    title: 'WhatsApp Us',
                    details: '+27 79 448 4159',
                    subtitle: 'Instant messaging for quick queries',
                    action: () => window.open('https://wa.me/***********', '_blank')
                  },
                  {
                    icon: CalendarDaysIcon,
                    title: 'Book a Call',
                    details: 'Schedule a free consultation',
                    subtitle: '30-minute strategy session',
                    action: () => window.open('https://calendly.com/ampd-deviq', '_blank')
                  },
                  {
                    icon: MapPinIcon,
                    title: 'Based In',
                    details: 'Cape Town, South Africa',
                    subtitle: 'Serving clients globally'
                  }
                ].map((contact, index) => (
                  <div
                    key={index}
                    onClick={contact.action}
                    className={`enhanced-card p-8 flex items-center space-x-6 ${contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''}`}
                  >
                    <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30">
                      <contact.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <div className="font-bold text-white text-xl mb-2">{contact.title}</div>
                      <div className="text-gray-300 text-lg mb-1">{contact.details}</div>
                      {contact.subtitle && (
                        <div className="text-gray-400 text-sm">{contact.subtitle}</div>
                      )}
                    </div>
                  </div>
                ))}
              </motion.div>

              {/* Contact Form */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="enhanced-card p-10"
              >
                <form onSubmit={handleContactSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <input
                      type="text"
                      name="name"
                      placeholder="Your Name"
                      required
                      className="bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                    />
                    <input
                      type="email"
                      name="email"
                      placeholder="Your Email"
                      required
                      className="bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                    />
                  </div>
                  <input
                    type="text"
                    name="company"
                    placeholder="Company Name"
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                  />
                  <select
                    name="budget"
                    title="Budget Range"
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                  >
                    <option value="" className="bg-slate-800">Select Budget Range</option>
                    <option value="Under R50,000" className="bg-slate-800">Under R50,000</option>
                    <option value="R50,000 - R150,000" className="bg-slate-800">R50,000 - R150,000</option>
                    <option value="R150,000 - R300,000" className="bg-slate-800">R150,000 - R300,000</option>
                    <option value="R300,000 - R500,000" className="bg-slate-800">R300,000 - R500,000</option>
                    <option value="R500,000+" className="bg-slate-800">R500,000+</option>
                  </select>
                  <textarea
                    name="message"
                    rows={6}
                    placeholder="Tell us about your project..."
                    required
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none"
                  ></textarea>
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50"
                  >
                    Send Message via WhatsApp
                  </button>
                </form>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Enhanced Footer */}
        <footer className="py-16 px-8 border-t border-white/10 relative">
          <div className="absolute inset-0 opacity-5">
            <img
              src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
              alt="Footer Background"
              className="w-full h-full object-cover"
            />
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
              {/* Company Info */}
              <div className="md:col-span-2">
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40">
                    <img src="/ampd-logo.png" alt="AMPD Dev-IQ" className="w-12 h-12 object-contain" />
                  </div>
                  <div>
                    <div className="text-2xl font-black text-white">AMPD Dev-IQ</div>
                    <div className="text-cyan-400">Smart Software. Limitless Potential.</div>
                  </div>
                </div>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  We are a Cape Town-based software development company specializing in custom web applications,
                  mobile apps, cloud solutions, and AI/ML implementations. Transform your business with our
                  cutting-edge technology solutions.
                </p>
                <div className="flex space-x-4">
                  <motion.a
                    href="https://wa.me/***********"
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.1 }}
                    className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center text-green-400 hover:bg-green-500/30 transition-colors"
                  >
                    <PhoneIcon className="w-5 h-5" />
                  </motion.a>
                  <motion.a
                    href="mailto:<EMAIL>"
                    whileHover={{ scale: 1.1 }}
                    className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-blue-500/30 transition-colors"
                  >
                    <EnvelopeIcon className="w-5 h-5" />
                  </motion.a>
                </div>
              </div>

              {/* Services */}
              <div>
                <h4 className="text-white font-bold text-lg mb-4">Services</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#services" className="hover:text-cyan-400 transition-colors">Web Development</a></li>
                  <li><a href="#services" className="hover:text-cyan-400 transition-colors">Mobile Apps</a></li>
                  <li><a href="#services" className="hover:text-cyan-400 transition-colors">Cloud Solutions</a></li>
                  <li><a href="#services" className="hover:text-cyan-400 transition-colors">AI/ML Solutions</a></li>
                  <li><a href="#services" className="hover:text-cyan-400 transition-colors">Enterprise Software</a></li>
                </ul>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-white font-bold text-lg mb-4">Quick Links</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#about" className="hover:text-cyan-400 transition-colors">About Us</a></li>
                  <li><a href="#portfolio" className="hover:text-cyan-400 transition-colors">Portfolio</a></li>
                  <li><a href="#contact" className="hover:text-cyan-400 transition-colors">Contact</a></li>
                  <li><a href="https://wa.me/***********" className="hover:text-cyan-400 transition-colors">Get Quote</a></li>
                  <li><a href="https://calendly.com/ampd-deviq" className="hover:text-cyan-400 transition-colors">Book Call</a></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-white/10 pt-8 text-center">
              <p className="text-gray-400 mb-2">
                © 2024 Developed by AMPD Dev-IQ. All rights reserved.
              </p>
              <p className="text-gray-500 text-sm">
                Built with ❤️ and smart software for limitless potential.
              </p>
            </div>
          </div>
        </footer>

        {/* Floating WhatsApp Button */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 2, type: "spring", stiffness: 260, damping: 20 }}
          className="fixed bottom-6 right-6 z-50"
        >
          <motion.a
            href="https://wa.me/***********?text=Hi! I'm interested in your software development services."
            target="_blank"
            rel="noopener noreferrer"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white shadow-2xl hover:bg-green-600 transition-colors group"
          >
            <PhoneIcon className="w-8 h-8 group-hover:animate-bounce" />
          </motion.a>
        </motion.div>
      </div>
    </div>
  );
};

export default ModernSinglePage;
