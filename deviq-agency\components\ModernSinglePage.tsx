'use client';

import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import {
  FloatingParticles,
  MorphingBackground,
  ParallaxSection,
  InteractiveBlob,
  AnimatedText,
  LiquidButton,
  GlitchText,
  useScrollAnimation
} from './AdvancedAnimations';
import {
  MovingShapes,
  CursorFollower,
  AnimatedGrid,
  FloatingTechIcons,
  MorphingBlob,
  ParticleSystem,
  HolographicCard,
  GlowingOrb
} from './MovingGraphics';
import { 
  SparklesIcon,
  ArrowRightIcon,
  PlayIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  CpuChipIcon,
  ShieldCheckIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  StarIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarDaysIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const ModernSinglePage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const services = [
    {
      icon: CodeBracketIcon,
      title: 'Web Development',
      description: 'Modern, responsive websites built with cutting-edge technologies',
      features: ['React/Next.js', 'TypeScript', 'Tailwind CSS', 'Progressive Web Apps']
    },
    {
      icon: DevicePhoneMobileIcon,
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile applications',
      features: ['React Native', 'Flutter', 'iOS/Android', 'App Store Optimization']
    },
    {
      icon: CloudIcon,
      title: 'Cloud Solutions',
      description: 'Scalable cloud infrastructure and deployment',
      features: ['AWS/Azure', 'Docker', 'Kubernetes', 'CI/CD Pipelines']
    },
    {
      icon: CpuChipIcon,
      title: 'AI/ML Solutions',
      description: 'Intelligent systems and machine learning integration',
      features: ['TensorFlow', 'PyTorch', 'Computer Vision', 'NLP']
    }
  ];

  const stats = [
    { number: '500+', label: 'Projects Delivered' },
    { number: '50+', label: 'Happy Clients' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support' }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      company: 'TechCorp',
      text: 'AMPD Dev-IQ transformed our digital presence completely. Outstanding work!',
      rating: 5
    },
    {
      name: 'Michael Chen',
      company: 'StartupXYZ',
      text: 'Professional, efficient, and delivered beyond our expectations.',
      rating: 5
    }
  ];

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const message = `Hi! I'm ${formData.get('name')} from ${formData.get('company')}.

Email: ${formData.get('email')}
Budget: ${formData.get('budget')}

Project Details:
${formData.get('message')}

I'd like to discuss this project with you.`;

    const whatsappUrl = `https://wa.me/***********?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Advanced Animation Components */}
      <FloatingParticles />
      <MorphingBackground />
      <MovingShapes />
      <CursorFollower />
      <AnimatedGrid />
      <FloatingTechIcons />
      <ParticleSystem />

      {/* Glowing Orbs */}
      <GlowingOrb size={150} color="#22d3ee" className="top-20 right-20" />
      <GlowingOrb size={100} color="#a855f7" className="bottom-40 left-20" />
      <GlowingOrb size={80} color="#f97316" className="top-1/2 left-1/3" />

      {/* Modern Background with Hero Image Integration */}
      <div className="fixed inset-0 z-0">
        {/* Enhanced Multi-Layer Background System */}
        <div className="absolute inset-0">
          {/* Primary Hero Background - High Quality Software Development */}
          <img
            src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
            alt="Software Development Background"
            className="w-full h-full object-cover opacity-15"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900/98 via-purple-900/95 to-slate-900/98"></div>
        </div>

        {/* Secondary Tech Animation Layer */}
        <div className="absolute inset-0 opacity-8">
          <img
            src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Tech Animation"
            className="w-full h-full object-cover mix-blend-overlay"
          />
        </div>

        {/* Animated Tech GIF Overlay */}
        <div className="absolute inset-0 opacity-12">
          <img
            src="https://i.pinimg.com/originals/36/e4/d0/36e4d0b856694fc471344b644a1dd6e4.gif"
            alt="Tech Animation"
            className="w-full h-full object-cover mix-blend-screen"
          />
        </div>

        {/* Enhanced Geometric Elements with Neon Colors */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-cyan-400/25 to-blue-600/25 rounded-full blur-3xl animate-blob"></div>
          <div className="absolute top-1/3 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/25 to-pink-600/25 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-1/3 left-1/4 w-72 h-72 bg-gradient-to-br from-orange-400/25 to-red-600/25 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-green-400/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse"></div>
        </div>

        {/* Enhanced Grid Overlay with Neon Effect */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(34,211,238,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(34,211,238,0.08)_1px,transparent_1px)] bg-[size:80px_80px] animate-pulse"></div>

        {/* Particle Effect Overlay */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/3 w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
          <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-ping animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-orange-400 rounded-full animate-ping animation-delay-4000"></div>
          <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-1000"></div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Modern Header */}
        <motion.header
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6 }}
          className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
            isScrolled ? 'bg-slate-900/95 backdrop-blur-md shadow-2xl' : 'bg-transparent'
          }`}
        >
          <nav className="container mx-auto px-8 py-6">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-2xl flex items-center justify-center backdrop-blur-xl border-2 border-cyan-400/40">
                  <img src="/ampd-logo.png" alt="AMPD Dev-IQ" className="w-12 h-12 object-contain" />
                </div>
                <div>
                  <div className="text-2xl font-black text-white">AMPD Dev-IQ</div>
                  <div className="text-sm text-cyan-400">Smart Software. Limitless Potential.</div>
                </div>
              </div>

              {/* Navigation */}
              <div className="hidden lg:flex items-center space-x-8">
                <a href="#services" className="text-white/80 hover:text-white transition-colors">Services</a>
                <a href="#about" className="text-white/80 hover:text-white transition-colors">About</a>
                <a href="#portfolio" className="text-white/80 hover:text-white transition-colors">Portfolio</a>
                <a href="#contact" className="text-white/80 hover:text-white transition-colors">Contact</a>
              </div>

              {/* CTA Button */}
              <div className="hidden lg:block">
                <a
                  href="#contact"
                  className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
                >
                  Get Started
                </a>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors"
              >
                {isMenuOpen ? (
                  <XMarkIcon className="w-6 h-6 text-white" />
                ) : (
                  <Bars3Icon className="w-6 h-6 text-white" />
                )}
              </button>
            </div>

            {/* Mobile Menu */}
            {isMenuOpen && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="lg:hidden mt-4 py-4 border-t border-white/20"
              >
                <div className="flex flex-col space-y-4">
                  <a href="#services" className="text-white/80 hover:text-white transition-colors py-2">Services</a>
                  <a href="#about" className="text-white/80 hover:text-white transition-colors py-2">About</a>
                  <a href="#portfolio" className="text-white/80 hover:text-white transition-colors py-2">Portfolio</a>
                  <a href="#contact" className="text-white/80 hover:text-white transition-colors py-2">Contact</a>
                  <a
                    href="#contact"
                    className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold px-6 py-3 rounded-lg text-center mt-4"
                  >
                    Get Started
                  </a>
                </div>
              </motion.div>
            )}
          </nav>
        </motion.header>

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-8 pt-24">
          <div className="max-w-7xl mx-auto text-center">
            {/* Enhanced Badge with Neon Glow */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-cyan-500/25 to-purple-500/25 backdrop-blur-xl border-2 border-cyan-400/40 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-cyan-500/20"
            >
              <SparklesIcon className="w-7 h-7 text-cyan-400 animate-pulse" />
              <span className="text-white font-bold text-lg neon-glow">Trusted by 500+ Companies Worldwide</span>
            </motion.div>

            {/* Enhanced Main Headline with Advanced Animations */}
            <div className="relative">
              <InteractiveBlob className="absolute inset-0 -z-10" />
              <AnimatedText
                text="SMART SOFTWARE."
                className="block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent"
                delay={0.2}
              />
              <GlitchText
                text="LIMITLESS"
                className="block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-4 leading-none tracking-tight bg-gradient-to-r from-purple-400 via-pink-500 to-orange-500 bg-clip-text text-transparent"
              />
              <AnimatedText
                text="POTENTIAL."
                className="block text-6xl md:text-8xl lg:text-9xl xl:text-ultra font-black font-poppins mb-12 leading-none tracking-tight bg-gradient-to-r from-green-400 via-cyan-500 to-blue-600 bg-clip-text text-transparent"
                delay={0.6}
              />
            </div>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 max-w-5xl mx-auto leading-relaxed"
            >
              We transform your <span className="text-cyan-400 font-semibold">business ideas</span> into 
              <span className="text-purple-400 font-semibold"> powerful digital solutions</span> that drive growth and innovation.
            </motion.p>

            {/* Enhanced CTA Buttons with Liquid Animation */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col md:flex-row gap-6 justify-center items-center mb-16"
            >
              <LiquidButton
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="text-xl px-12 py-6 shadow-2xl"
              >
                <div className="flex items-center space-x-3">
                  <span>Start Your Project</span>
                  <ArrowRightIcon className="w-6 h-6" />
                </div>
              </LiquidButton>

              <motion.a
                href="#about"
                className="group bg-white/10 backdrop-blur-md border-2 border-white/20 hover:bg-white/20 text-white font-bold text-xl px-12 py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center space-x-3"
                whileHover={{
                  scale: 1.05,
                  boxShadow: "0 0 30px rgba(34, 211, 238, 0.5)"
                }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  className="w-8 h-8 bg-gradient-to-r from-cyan-400 to-purple-500 rounded-full flex items-center justify-center"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.5 }}
                >
                  <PlayIcon className="w-4 h-4 ml-0.5" />
                </motion.div>
                <span>Watch Demo</span>
              </motion.a>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl md:text-5xl font-black bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-300 font-semibold">{stat.label}</div>
                </div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* Enhanced Services Section with Background and Parallax */}
        <ParallaxSection speed={0.3}>
          <section id="services" className="py-32 px-8 relative">
          {/* Section Background Image */}
          <div className="absolute inset-0 opacity-5">
            <img
              src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="Team Collaboration"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 to-cyan-900/80"></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-24"
            >
              <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent neon-glow">
                OUR SERVICES
              </h2>
              <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed">
                Comprehensive digital solutions to transform your business with cutting-edge technology
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-10 lg:gap-12">
              {services.map((service, index) => (
                <HolographicCard
                  key={index}
                  className="group enhanced-card hover:border-cyan-400/50 relative overflow-hidden"
                >
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                  {/* Card Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-600/20 rounded-3xl"></div>
                  </div>

                  {/* Enhanced Icon Container */}
                  <div className="relative z-10">
                    <div className="w-20 h-20 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-3xl flex items-center justify-center mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-2xl shadow-cyan-500/30">
                      <service.icon className="w-10 h-10 text-white" />
                    </div>

                    {/* Enhanced Typography */}
                    <h3 className="text-3xl lg:text-4xl font-bold text-white mb-6 group-hover:text-cyan-400 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-xl text-gray-300 mb-8 leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                      {service.description}
                    </p>

                    {/* Enhanced Feature List */}
                    <ul className="space-y-4">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-lg text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                          <CheckCircleIcon className="w-6 h-6 text-cyan-400 mr-4 group-hover:text-cyan-300 transition-colors duration-300" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </motion.div>
                </HolographicCard>
              ))}
            </div>
          </div>
        </section>
        </ParallaxSection>

        {/* Enhanced About Section */}
        <section id="about" className="py-32 px-8 relative">
          {/* Section Background */}
          <div className="absolute inset-0 opacity-5">
            <img
              src="https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="Team Working"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-purple-900/80 to-orange-900/80"></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-content"
              >
                <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-purple-400 to-orange-500 bg-clip-text text-transparent neon-glow">
                  ABOUT US
                </h2>
                <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 mb-12 leading-relaxed">
                  At <span className="text-cyan-400 font-bold ampd-glow">AMPD Dev-IQ</span>, we believe that smart software has the power to unlock limitless potential in every business.
                  Our mission is to amplify your success through intelligent solutions and innovative technology.
                </p>
                <div className="grid grid-cols-2 gap-10">
                  <div className="text-center enhanced-card p-6">
                    <div className="text-5xl lg:text-6xl font-black text-cyan-400 mb-4 neon-glow">98%</div>
                    <div className="text-xl text-gray-300">Client Satisfaction</div>
                  </div>
                  <div className="text-center enhanced-card p-6">
                    <div className="text-5xl lg:text-6xl font-black text-purple-400 mb-4 neon-glow">100%</div>
                    <div className="text-xl text-gray-300">On-Time Delivery</div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="grid grid-cols-2 gap-6"
              >
                {[
                  { icon: RocketLaunchIcon, title: 'Innovation', desc: 'Cutting-edge solutions' },
                  { icon: ShieldCheckIcon, title: 'Quality', desc: 'Premium standards' },
                  { icon: CheckCircleIcon, title: 'Reliability', desc: 'Trusted delivery' },
                  { icon: StarIcon, title: 'Excellence', desc: 'Outstanding results' }
                ].map((item, index) => (
                  <div key={index} className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <item.icon className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="text-lg font-bold text-white mb-2">{item.title}</h4>
                    <p className="text-gray-400 text-sm">{item.desc}</p>
                  </div>
                ))}
              </motion.div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-32 px-8">
          <div className="max-w-7xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-20"
            >
              <h2 className="text-5xl md:text-7xl font-black font-poppins mb-8 bg-gradient-to-r from-green-400 to-cyan-500 bg-clip-text text-transparent">
                CLIENT TESTIMONIALS
              </h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8"
                >
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-200 text-lg mb-6 italic">"{testimonial.text}"</p>
                  <div>
                    <div className="font-bold text-white">{testimonial.name}</div>
                    <div className="text-cyan-400">{testimonial.company}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Enhanced Contact Section */}
        <section id="contact" className="py-32 px-8 relative">
          {/* Section Background */}
          <div className="absolute inset-0 opacity-5">
            <img
              src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2126&q=80"
              alt="Contact Background"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-orange-900/80 to-red-900/80"></div>
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-24"
            >
              <h2 className="text-5xl md:text-7xl lg:text-8xl font-black font-poppins mb-10 bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent neon-glow">
                GET IN TOUCH
              </h2>
              <p className="text-2xl md:text-3xl lg:text-4xl text-gray-200 max-w-5xl mx-auto leading-relaxed">
                Ready to transform your business? Let&apos;s discuss your project and unlock your potential.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              {/* Contact Info */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                {[
                  {
                    icon: EnvelopeIcon,
                    title: 'Email Us',
                    details: '<EMAIL>',
                    action: () => window.open('mailto:<EMAIL>', '_blank')
                  },
                  {
                    icon: PhoneIcon,
                    title: 'WhatsApp Us',
                    details: '+27 79 448 4159',
                    action: () => window.open('https://wa.me/***********', '_blank')
                  },
                  {
                    icon: MapPinIcon,
                    title: 'Based In',
                    details: 'Cape Town, South Africa'
                  }
                ].map((contact, index) => (
                  <div
                    key={index}
                    onClick={contact.action}
                    className={`enhanced-card p-8 flex items-center space-x-6 ${contact.action ? 'cursor-pointer hover:border-cyan-400/50 hover:shadow-2xl hover:shadow-cyan-500/20' : ''}`}
                  >
                    <div className="w-16 h-16 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyan-500/30">
                      <contact.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <div className="font-bold text-white text-xl mb-2">{contact.title}</div>
                      <div className="text-gray-300 text-lg">{contact.details}</div>
                    </div>
                  </div>
                ))}
              </motion.div>

              {/* Contact Form */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="enhanced-card p-10"
              >
                <form onSubmit={handleContactSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <input
                      type="text"
                      name="name"
                      placeholder="Your Name"
                      required
                      className="bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                    />
                    <input
                      type="email"
                      name="email"
                      placeholder="Your Email"
                      required
                      className="bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                    />
                  </div>
                  <input
                    type="text"
                    name="company"
                    placeholder="Company Name"
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                  />
                  <select
                    name="budget"
                    title="Budget Range"
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300"
                  >
                    <option value="" className="bg-slate-800">Select Budget Range</option>
                    <option value="Under R50,000" className="bg-slate-800">Under R50,000</option>
                    <option value="R50,000 - R150,000" className="bg-slate-800">R50,000 - R150,000</option>
                    <option value="R150,000 - R300,000" className="bg-slate-800">R150,000 - R300,000</option>
                    <option value="R300,000 - R500,000" className="bg-slate-800">R300,000 - R500,000</option>
                    <option value="R500,000+" className="bg-slate-800">R500,000+</option>
                  </select>
                  <textarea
                    name="message"
                    rows={6}
                    placeholder="Tell us about your project..."
                    required
                    className="w-full bg-white/10 backdrop-blur-xl border-2 border-white/30 rounded-2xl px-8 py-6 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-cyan-400 focus:shadow-2xl focus:shadow-cyan-500/20 transition-all duration-300 resize-none"
                  ></textarea>
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white font-bold text-2xl py-6 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl shadow-cyan-500/30 hover:shadow-cyan-500/50"
                  >
                    Send Message via WhatsApp
                  </button>
                </form>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-16 px-8 border-t border-white/10">
          <div className="max-w-7xl mx-auto text-center">
            <div className="flex items-center justify-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-gradient-to-br from-cyan-400/20 to-purple-600/20 rounded-xl flex items-center justify-center">
                <img src="/ampd-logo.png" alt="AMPD Dev-IQ" className="w-8 h-8 object-contain" />
              </div>
              <div>
                <div className="text-xl font-black text-white">AMPD Dev-IQ</div>
                <div className="text-sm text-cyan-400">Smart Software. Limitless Potential.</div>
              </div>
            </div>
            <p className="text-gray-400 mb-4">
              © 2024 Developed by AMPD Dev-IQ. All rights reserved.
            </p>
            <p className="text-gray-500 text-sm">
              Built with ❤️ and smart software for limitless potential.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default ModernSinglePage;
