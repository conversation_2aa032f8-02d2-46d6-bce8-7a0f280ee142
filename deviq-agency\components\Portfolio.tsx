'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { ArrowTopRightOnSquareIcon, EyeIcon } from '@heroicons/react/24/outline';

const Portfolio = () => {
  const [activeFilter, setActiveFilter] = useState('All');

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      category: 'Web Development',
      description: 'A modern e-commerce platform with AI-powered recommendations and real-time inventory management.',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS'],
      problem: 'Client needed a scalable e-commerce solution to handle 10,000+ concurrent users.',
      solution: 'Built a microservices architecture with real-time analytics and AI recommendations.',
      impact: '300% increase in sales, 50% reduction in cart abandonment',
      testimonial: {
        text: "DevIQ transformed our online presence completely. Sales increased by 300% within 6 months.",
        author: "<PERSON>",
        position: "CEO, TechStore"
      }
    },
    {
      id: 2,
      title: 'Healthcare Management System',
      category: 'Enterprise Software',
      description: 'Comprehensive healthcare management system with patient records, appointment scheduling, and telemedicine.',
      image: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop',
      technologies: ['React', 'Python', 'MongoDB', 'Docker'],
      problem: 'Hospital needed to digitize patient records and streamline operations.',
      solution: 'Developed a HIPAA-compliant system with role-based access and real-time notifications.',
      impact: '60% reduction in administrative time, improved patient satisfaction',
      testimonial: {
        text: "The system has revolutionized how we manage patient care. Highly recommended!",
        author: "Dr. Michael Chen",
        position: "Chief Medical Officer"
      }
    },
    {
      id: 3,
      title: 'FinTech Mobile App',
      category: 'Mobile Development',
      description: 'Secure mobile banking app with biometric authentication and real-time transaction monitoring.',
      image: 'https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop',
      technologies: ['React Native', 'Node.js', 'PostgreSQL', 'AWS'],
      problem: 'Bank needed a modern mobile app to compete with digital-first competitors.',
      solution: 'Created a feature-rich app with advanced security and intuitive UX.',
      impact: '200% increase in mobile transactions, 4.8/5 app store rating',
      testimonial: {
        text: "Our customers love the new app. It's intuitive, secure, and feature-rich.",
        author: "Jennifer Williams",
        position: "Digital Banking Director"
      }
    },
    {
      id: 4,
      title: 'AI-Powered Analytics Dashboard',
      category: 'AI/ML',
      description: 'Real-time analytics dashboard with machine learning insights for business intelligence.',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=600&h=400&fit=crop',
      technologies: ['Python', 'TensorFlow', 'React', 'PostgreSQL'],
      problem: 'Company needed actionable insights from massive datasets.',
      solution: 'Built ML models for predictive analytics with an intuitive dashboard.',
      impact: '40% improvement in decision-making speed, $2M cost savings',
      testimonial: {
        text: "The AI insights have transformed our business strategy. Incredible ROI!",
        author: "Robert Davis",
        position: "VP of Operations"
      }
    },
    {
      id: 5,
      title: 'Smart IoT Platform',
      category: 'IoT',
      description: 'IoT platform for smart building management with energy optimization and predictive maintenance.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=400&fit=crop',
      technologies: ['Node.js', 'MongoDB', 'MQTT', 'AWS IoT'],
      problem: 'Building management company needed to optimize energy consumption.',
      solution: 'Developed IoT sensors network with ML-powered optimization algorithms.',
      impact: '35% reduction in energy costs, 90% uptime improvement',
      testimonial: {
        text: "The IoT platform has exceeded our expectations. Amazing energy savings!",
        author: "Lisa Anderson",
        position: "Facilities Manager"
      }
    },
    {
      id: 6,
      title: 'Educational Learning Platform',
      category: 'Web Development',
      description: 'Interactive online learning platform with video streaming, assessments, and progress tracking.',
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop',
      technologies: ['Next.js', 'Node.js', 'MongoDB', 'AWS'],
      problem: 'Educational institution needed a comprehensive online learning solution.',
      solution: 'Built a scalable platform with interactive content and real-time collaboration.',
      impact: '500% increase in student engagement, 95% completion rate',
      testimonial: {
        text: "Students love the platform. Engagement has never been higher!",
        author: "Prof. David Wilson",
        position: "Academic Director"
      }
    }
  ];

  const categories = ['All', 'Web Development', 'Mobile Development', 'Enterprise Software', 'AI/ML', 'IoT'];

  const filteredProjects = activeFilter === 'All' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <section id="portfolio" className="py-20 relative">
      {/* Full-width content container */}
      <div className="full-width-content relative z-10">
        <div className="max-w-none px-8 md:px-16 lg:px-24">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center space-x-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md border border-blue-400/30 rounded-full px-10 py-5 mb-12 shadow-2xl shadow-blue-500/20">
            <span className="text-blue-400 font-semibold text-2xl">Our Portfolio</span>
          </div>
          <h2 className="text-mega font-black font-poppins mb-12 leading-none tracking-tight neon-glow">
            <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-600 bg-clip-text text-transparent drop-shadow-2xl">
              OUR PORTFOLIO
            </span>
          </h2>
          <p className="text-3xl md:text-4xl lg:text-5xl text-gray-200 max-w-7xl mx-auto leading-relaxed font-light">
            Explore our <span className="text-blue-400 font-semibold">successful projects</span> and see how we've helped businesses
            <span className="text-purple-400 font-semibold"> transform their digital presence</span>.
          </p>
        </motion.div>

        {/* Enhanced Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-6 mb-16"
        >
          {categories.map((category) => (
            <button
              key={category}
              type="button"
              onClick={() => setActiveFilter(category)}
              className={`px-10 py-5 rounded-2xl font-bold text-xl transition-all duration-300 backdrop-blur-md shadow-xl ${
                activeFilter === category
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white border-2 border-blue-400/50 shadow-blue-500/50'
                  : 'bg-gradient-to-r from-white/10 to-white/5 text-white border-2 border-white/30 hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-400/50'
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {filteredProjects.map((project, index) => (
            <motion.div
              key={project.id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-md border border-white/30 rounded-3xl overflow-hidden shadow-2xl hover:shadow-cyan-500/30 transition-all duration-500 group transform hover:-translate-y-4 hover:scale-105"
            >
              {/* Enhanced Project Image */}
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-6">
                    <button
                      type="button"
                      className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white p-4 rounded-2xl hover:from-cyan-400 hover:to-blue-500 transition-all duration-300 shadow-xl"
                      aria-label="View project details"
                      title="View project details"
                    >
                      <EyeIcon className="w-6 h-6" />
                    </button>
                    <button
                      type="button"
                      className="bg-gradient-to-r from-purple-500 to-pink-600 text-white p-4 rounded-2xl hover:from-purple-400 hover:to-pink-500 transition-all duration-300 shadow-xl"
                      aria-label="Open project in new tab"
                      title="Open project in new tab"
                    >
                      <ArrowTopRightOnSquareIcon className="w-6 h-6" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Enhanced Project Content */}
              <div className="p-8">
                <div className="flex items-center justify-between mb-6">
                  <span className={`text-lg font-bold px-4 py-2 rounded-2xl ${
                    project.category === 'Web Development' ? 'text-cyan-400 bg-cyan-500/20 border border-cyan-400/30' :
                    project.category === 'Mobile Development' ? 'text-purple-400 bg-purple-500/20 border border-purple-400/30' :
                    project.category === 'Enterprise Software' ? 'text-green-400 bg-green-500/20 border border-green-400/30' :
                    project.category === 'AI/ML' ? 'text-pink-400 bg-pink-500/20 border border-pink-400/30' :
                    'text-blue-400 bg-blue-500/20 border border-blue-400/30'
                  }`}>
                    {project.category}
                  </span>
                </div>

                <h3 className="text-3xl font-bold font-poppins mb-6 text-white neon-glow">
                  {project.title}
                </h3>

                <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                  {project.description}
                </p>

                {/* Enhanced Technologies */}
                <div className="flex flex-wrap gap-3 mb-6">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="text-sm bg-gradient-to-r from-white/20 to-white/10 text-white px-3 py-2 rounded-xl border border-white/20 font-semibold"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Enhanced Impact */}
                <div className="border-t border-white/20 pt-6">
                  <p className="text-lg font-bold text-cyan-400 mb-3">IMPACT:</p>
                  <p className="text-lg text-gray-200 font-semibold">{project.impact}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-24"
        >
          <p className="text-3xl md:text-4xl text-gray-200 mb-12 leading-relaxed">
            Ready to see <span className="text-blue-400 font-semibold">your project featured here</span>?
          </p>
          <button type="button" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-400 hover:to-purple-500 text-white font-bold text-3xl px-16 py-8 rounded-2xl transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-blue-500/50 border-2 border-blue-400/30">
            START YOUR PROJECT
          </button>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
